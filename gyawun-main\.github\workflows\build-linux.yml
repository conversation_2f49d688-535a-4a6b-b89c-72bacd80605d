name: Build Linux

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  # build-linux:
  #   name: Build Linux Binary
  #   runs-on: ubuntu-latest
  #   steps:
  #     - name: Checkout repository
  #       uses: actions/checkout@v4.1.1


  #     - name: Set up Flutter
  #       uses: subosito/flutter-action@v2.16.0
  #       with:
  #         channel: "stable"
  #         flutter-version: "3.29.3"
      
  #     - name: Enable Linux platform (if not already enabled)
  #       run: flutter create --platforms=linux .

  #     - name: Get dependencies
  #       run: flutter pub get

  #     - name: Install Linux build dependencies
  #       run: |
  #         sudo apt-get update -y
  #         sudo apt-get install -y ninja-build libgtk-3-dev libmpv-dev patchelf cmake clang libfuse2

  #     - name: Build for Linux
  #       run: flutter build linux

  #     - name: Upload build artifacts
  #       uses: actions/upload-artifact@v4
  #       with:
  #         name: gyawunmusic-linux
  #         path: build/linux/x64/release/bundle

  # build-linux-deb:
  #   name: Build Debian Package
  #   runs-on: ubuntu-latest
  #   needs: [build-linux]
  #   steps:
  #     - name: Checkout repository
  #       uses: actions/checkout@v4.1.1

  #     - name: Download Linux Build Artifact
  #       uses: actions/download-artifact@v4
  #       with:
  #         name: gyawunmusic-linux
  #         path: build/linux/x64/release/bundle

  #     - name: Install Packaging Tools
  #       run: |
  #         sudo apt-get update -y
  #         sudo apt-get install -y dpkg-dev fakeroot

  #     - name: Inject Flutter Binary into Packaging Folder
  #       run: |
  #         mkdir -p packaging/debian/usr/local/bin
  #         cp -r build/linux/x64/release/bundle/* packaging/debian/usr/local/bin/
  #         chmod 755 packaging/debian/usr/local/bin/gyawun

  #     - name: Build DEB Package
  #       run: |
  #         dpkg-deb --build packaging/debian
  #         mv packaging/debian.deb GyawunMusic-linux-amd64.deb

  #     - name: Upload DEB Package
  #       uses: actions/upload-artifact@v4
  #       with:
  #         name: gyawunmusic-deb
  #         path: GyawunMusic-linux-amd64.deb


    
  # build-linux-flatpak:
  #   name: Build Flatpak Bundle
  #   runs-on: ubuntu-latest
  #   needs: [build-linux]
  #   container:
  #     image: ghcr.io/flathub-infra/flatpak-github-actions:gnome-48
  #     options: --privileged
  #   steps:
  #     - name: Checkout repository
  #       uses: actions/checkout@v4.1.1
  #       with:
  #         submodules: true

  #     - name: Download Artifacts
  #       uses: actions/download-artifact@v4
  #       with:
  #         name: gyawunmusic-linux
  #         path: packaging/flatpak/bundle
      

  #     - name: Build Flatpak Bundle
  #       uses: flatpak/flatpak-github-actions/flatpak-builder@v6
  #       with:
  #         bundle: GyawunMusic-Linux.flatpak
  #         manifest-path: packaging/flatpak/com.jhelum.gyawun.yaml

  build-linux-snap:
    name: Build Snap Package
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4


      - name: Set up Flutter
        uses: subosito/flutter-action@v2.16.0
        with:
          channel: stable
          flutter-version: "3.29.3"

      - name: Enable Linux platform (if not already enabled)
        run: flutter create --platforms=linux .

      - name: Build Snap with Snapcraft
        uses: snapcore/action-build@v1
        id: snapcraft

      - name: Upload Snap artifact
        uses: actions/upload-artifact@v4
        with:
          name: gyawunmusic-snap
          path: ${{ steps.snapcraft.outputs.snap }}