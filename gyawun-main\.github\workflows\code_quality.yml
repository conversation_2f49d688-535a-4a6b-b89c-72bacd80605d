name: Code quality check
on:
  workflow_dispatch:
  pull_request:
    branches:
      - main
jobs:
  text:
    name: Check the source code quality
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: subosito/flutter-action@v2.16.0
        with:
          channel: stable
          flutter-version: 3.22.2
      - uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: 17.0.10
      - name: Install packages
        run: flutter pub get
      - name: Linter
        run: flutter analyze
      - name: build check
        run: flutter build apk
      - uses: actions/upload-artifact@v4
        with:
          name: release-apk
          path: build/app/outputs/apk/release/app-release.apk
