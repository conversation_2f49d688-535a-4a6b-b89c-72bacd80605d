{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/home/<USER>/Apps/Android/Sdk/cmake/3.22.1/bin/cmake", "cpack": "/home/<USER>/Apps/Android/Sdk/cmake/3.22.1/bin/cpack", "ctest": "/home/<USER>/Apps/Android/Sdk/cmake/3.22.1/bin/ctest", "root": "/home/<USER>/Apps/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": false, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-cfe4695e156db7cbab1f.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-c525d5c3ff184bbd2b01.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-1448864c0408dbe26de9.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-c525d5c3ff184bbd2b01.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-1448864c0408dbe26de9.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-cfe4695e156db7cbab1f.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}