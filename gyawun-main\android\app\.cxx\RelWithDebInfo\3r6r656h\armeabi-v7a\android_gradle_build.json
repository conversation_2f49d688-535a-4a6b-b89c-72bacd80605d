{"buildFiles": ["/home/<USER>/Apps/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/home/<USER>/Apps/Android/Sdk/cmake/3.22.1/bin/ninja", "-C", "/home/<USER>/Development/Jhelum/gyawun-app/android/app/.cxx/RelWithDebInfo/3r6r656h/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/home/<USER>/Apps/Android/Sdk/cmake/3.22.1/bin/ninja", "-C", "/home/<USER>/Development/Jhelum/gyawun-app/android/app/.cxx/RelWithDebInfo/3r6r656h/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "/home/<USER>/Apps/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/linux-x86_64/bin/clang.lld", "cppCompilerExecutable": "/home/<USER>/Apps/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": []}