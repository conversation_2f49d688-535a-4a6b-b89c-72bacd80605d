set(CMAKE_HOST_SYSTEM "Linux-6.11.0-19-generic")
set(CMAKE_HOST_SYSTEM_NAME "Linux")
set(CMAKE_HOST_SYSTEM_VERSION "6.11.0-19-generic")
set(CMAKE_HOST_SYSTEM_PROCESSOR "x86_64")

include("/home/<USER>/Apps/Android/Sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake")

set(CMAKE_SYSTEM "Android-1")
set(CMAKE_SYSTEM_NAME "Android")
set(CMAKE_SYSTEM_VERSION "1")
set(CMAKE_SYSTEM_PROCESSOR "x86_64")

set(CMAKE_CROSSCOMPILING "TRUE")

set(CMAKE_SYSTEM_LOADED 1)
