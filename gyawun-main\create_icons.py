#!/usr/bin/env python3
"""
Simple script to create placeholder PNG icons for Android app
"""

from PIL import Image, ImageDraw
import os

def create_icon(size, output_path, color=(29, 185, 84)):
    """Create a simple circular icon with the CODO green color"""
    # Create a new image with transparent background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Draw a circle with CODO green color
    margin = size // 8
    draw.ellipse([margin, margin, size - margin, size - margin], fill=color)
    
    # Draw a music note symbol in white
    note_size = size // 3
    note_x = size // 2 - note_size // 4
    note_y = size // 2 - note_size // 2
    
    # Simple music note shape
    draw.ellipse([note_x, note_y + note_size // 2, note_x + note_size // 3, note_y + note_size], fill=(255, 255, 255))
    draw.rectangle([note_x + note_size // 4, note_y, note_x + note_size // 3, note_y + note_size // 2 + note_size // 4], fill=(255, 255, 255))
    
    # Save the image
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    img.save(output_path, 'PNG')
    print(f"Created icon: {output_path}")

def create_splash(width, height, output_path):
    """Create a simple splash screen"""
    img = Image.new('RGB', (width, height), (18, 18, 18))  # Dark background
    draw = ImageDraw.Draw(img)
    
    # Draw CODO logo in center
    center_x, center_y = width // 2, height // 2
    logo_size = min(width, height) // 4
    
    # Draw circle
    draw.ellipse([center_x - logo_size, center_y - logo_size, 
                  center_x + logo_size, center_y + logo_size], 
                 fill=(29, 185, 84))
    
    # Save the image
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    img.save(output_path, 'PNG')
    print(f"Created splash: {output_path}")

# Create the missing icons
base_path = "android/app/src/main/res"

# App launcher icons
create_icon(48, f"{base_path}/mipmap-mdpi/ic_launcher.png")
create_icon(72, f"{base_path}/mipmap-hdpi/ic_launcher_monochrome.png", (255, 255, 255))

# Splash screen
create_splash(480, 800, f"{base_path}/drawable-night-xxhdpi/android12splash.png")

print("All icons created successfully!")
