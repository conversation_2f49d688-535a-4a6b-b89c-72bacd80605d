// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(count) =>
      "${Intl.plural(count, zero: 'No Songs', one: '1 Song', other: '${count} Songs')}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "About": MessageLookupByLibrary.simpleMessage("About"),
    "Add_To_Favourites": MessageLookupByLibrary.simpleMessage(
      "Add To Favourites",
    ),
    "Add_To_Library": MessageLookupByLibrary.simpleMessage("Add To Library"),
    "Add_To_Playlist": MessageLookupByLibrary.simpleMessage("Add To Playlist"),
    "Add_To_Queue": MessageLookupByLibrary.simpleMessage("Add To Queue"),
    "Album": MessageLookupByLibrary.simpleMessage("Album"),
    "Albums": MessageLookupByLibrary.simpleMessage("Albums"),
    "Appearence": MessageLookupByLibrary.simpleMessage("Appearence"),
    "Artists": MessageLookupByLibrary.simpleMessage("Artists"),
    "Audio_And_Playback": MessageLookupByLibrary.simpleMessage(
      "Audio and Playback",
    ),
    "Backup": MessageLookupByLibrary.simpleMessage("Backup"),
    "Backup_And_Restore": MessageLookupByLibrary.simpleMessage(
      "Backup and Restore",
    ),
    "Battery_Optimisation_message": MessageLookupByLibrary.simpleMessage(
      "Click here disable battery optimisation for Gyawun to work properly",
    ),
    "Battery_Optimisation_title": MessageLookupByLibrary.simpleMessage(
      "Battery Optimisation Detected",
    ),
    "Bug_Report": MessageLookupByLibrary.simpleMessage("Bug Report"),
    "Buy_Me_A_Coffee": MessageLookupByLibrary.simpleMessage("Buy me a Coffee"),
    "Cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "Check_For_Update": MessageLookupByLibrary.simpleMessage(
      "Check for Update",
    ),
    "Confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
    "Content": MessageLookupByLibrary.simpleMessage("Content"),
    "Contributors": MessageLookupByLibrary.simpleMessage("Contributors"),
    "Copied_To_Clipboard": MessageLookupByLibrary.simpleMessage(
      "Copied to Clipboard",
    ),
    "Country": MessageLookupByLibrary.simpleMessage("Country"),
    "Create": MessageLookupByLibrary.simpleMessage("Create"),
    "Create_Playlist": MessageLookupByLibrary.simpleMessage("Create Playlist"),
    "DOwnload_Quality": MessageLookupByLibrary.simpleMessage(
      "Download Quality",
    ),
    "Delete_Item_Message": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this item?",
    ),
    "Delete_Playback_History": MessageLookupByLibrary.simpleMessage(
      "Delete Playback History",
    ),
    "Delete_Playback_History_Confirm_Message":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure you want to delete Playback History.",
        ),
    "Delete_Search_History": MessageLookupByLibrary.simpleMessage(
      "Delete Search History",
    ),
    "Delete_Search_History_Confirm_Message":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure you want to delete Search History.",
        ),
    "Developer": MessageLookupByLibrary.simpleMessage("Developer"),
    "Donate": MessageLookupByLibrary.simpleMessage("Donate"),
    "Donate_Message": MessageLookupByLibrary.simpleMessage(
      "Support the development of Gyawun",
    ),
    "Done": MessageLookupByLibrary.simpleMessage("Done"),
    "Download": MessageLookupByLibrary.simpleMessage("Download"),
    "Downloads": MessageLookupByLibrary.simpleMessage("Downloads"),
    "Dynamic_Colors": MessageLookupByLibrary.simpleMessage("Dynamic Colors"),
    "Enable_Equalizer": MessageLookupByLibrary.simpleMessage(
      "Enable Equalizer",
    ),
    "Enable_Playback_History": MessageLookupByLibrary.simpleMessage(
      "Enable Playback History",
    ),
    "Enable_Search_History": MessageLookupByLibrary.simpleMessage(
      "Enable Search History",
    ),
    "Enter_Visitor_Id": MessageLookupByLibrary.simpleMessage(
      "Enter Visitor Id",
    ),
    "Equalizer": MessageLookupByLibrary.simpleMessage("Equalizer"),
    "Favourites": MessageLookupByLibrary.simpleMessage("Favourites"),
    "Feature_Request": MessageLookupByLibrary.simpleMessage("Feature Request"),
    "Google_Account": MessageLookupByLibrary.simpleMessage("Google Account"),
    "Gyawun": MessageLookupByLibrary.simpleMessage("Gyawun"),
    "High": MessageLookupByLibrary.simpleMessage("High"),
    "History": MessageLookupByLibrary.simpleMessage("History"),
    "Home": MessageLookupByLibrary.simpleMessage("Home"),
    "Import": MessageLookupByLibrary.simpleMessage("Import"),
    "Import_Playlist": MessageLookupByLibrary.simpleMessage("Import Playlist"),
    "Jhelum_Corp": MessageLookupByLibrary.simpleMessage("Jhelum Corp"),
    "Language": MessageLookupByLibrary.simpleMessage("Language"),
    "Loudness_And_Equalizer": MessageLookupByLibrary.simpleMessage(
      "Loudness And Equalizer",
    ),
    "Loudness_Enhancer": MessageLookupByLibrary.simpleMessage(
      "Loudness Enhancer",
    ),
    "Low": MessageLookupByLibrary.simpleMessage("Low"),
    "Made_In_Kashmir": MessageLookupByLibrary.simpleMessage("Made in Kashmir"),
    "Name": MessageLookupByLibrary.simpleMessage("Name"),
    "Next_Up": MessageLookupByLibrary.simpleMessage("Next Up"),
    "No": MessageLookupByLibrary.simpleMessage("No"),
    "Organisation": MessageLookupByLibrary.simpleMessage("Organisation"),
    "Pay_With_UPI": MessageLookupByLibrary.simpleMessage("Pay with UPI"),
    "Payment_Methods": MessageLookupByLibrary.simpleMessage("Payment Methods"),
    "Personalised_Content": MessageLookupByLibrary.simpleMessage(
      "Personalised Content",
    ),
    "Play_Next": MessageLookupByLibrary.simpleMessage("Play Next"),
    "Playback_History_Deleted": MessageLookupByLibrary.simpleMessage(
      "Playback History Deleted",
    ),
    "Playlist_Name": MessageLookupByLibrary.simpleMessage("Playlist Name"),
    "Playlists": MessageLookupByLibrary.simpleMessage("Playlists"),
    "Progress": MessageLookupByLibrary.simpleMessage("Progress"),
    "Remove": MessageLookupByLibrary.simpleMessage("Remove"),
    "Remove_All_History_Message": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to clear all history?",
    ),
    "Remove_From_Favourites": MessageLookupByLibrary.simpleMessage(
      "Remove From Favourites",
    ),
    "Remove_From_Library": MessageLookupByLibrary.simpleMessage(
      "Remove From Library",
    ),
    "Remove_From_YTMusic_Message": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to remove it from YTMusic?",
    ),
    "Remove_Message": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to remove it?",
    ),
    "Rename": MessageLookupByLibrary.simpleMessage("Rename"),
    "Rename_Playlist": MessageLookupByLibrary.simpleMessage("Rename Playlist"),
    "Reset_Visitor_Id": MessageLookupByLibrary.simpleMessage(
      "Reset Visitor Id",
    ),
    "Restore": MessageLookupByLibrary.simpleMessage("Restore"),
    "Saved": MessageLookupByLibrary.simpleMessage("Saved"),
    "Search_Gyawun": MessageLookupByLibrary.simpleMessage("Search Gyawun"),
    "Search_History_Deleted": MessageLookupByLibrary.simpleMessage(
      "Search History Deleted",
    ),
    "Search_Settings": MessageLookupByLibrary.simpleMessage("Search Settings"),
    "Select_Backup": MessageLookupByLibrary.simpleMessage("Select Backup"),
    "Settings": MessageLookupByLibrary.simpleMessage("Settings"),
    "Sheikh_Haziq": MessageLookupByLibrary.simpleMessage("Sheikh Haziq"),
    "Show_Less": MessageLookupByLibrary.simpleMessage("Show Less"),
    "Show_More": MessageLookupByLibrary.simpleMessage("Show More"),
    "Shuffle": MessageLookupByLibrary.simpleMessage("Shuffle"),
    "Skip_Silence": MessageLookupByLibrary.simpleMessage("Skip Silence"),
    "Sleep_Timer": MessageLookupByLibrary.simpleMessage("Sleep Timer"),
    "Songs": MessageLookupByLibrary.simpleMessage("Songs"),
    "Songs_Will_Start_Playing_Soon": MessageLookupByLibrary.simpleMessage(
      "Songs will start playing soon.",
    ),
    "Source_Code": MessageLookupByLibrary.simpleMessage("Source Code"),
    "Start_Radio": MessageLookupByLibrary.simpleMessage("Start Radio"),
    "Streaming_Quality": MessageLookupByLibrary.simpleMessage(
      "Streaming Quality",
    ),
    "Subscriptions": MessageLookupByLibrary.simpleMessage("Subscriptions"),
    "Support_Me_On_Kofi": MessageLookupByLibrary.simpleMessage(
      "Support me on Ko-fi",
    ),
    "Telegram": MessageLookupByLibrary.simpleMessage("Telegram"),
    "Theme_Mode": MessageLookupByLibrary.simpleMessage("Theme Mode"),
    "Version": MessageLookupByLibrary.simpleMessage("Version"),
    "Visitor_Id": MessageLookupByLibrary.simpleMessage("Visitor Id"),
    "Window_Effect": MessageLookupByLibrary.simpleMessage("Window Effect"),
    "YTMusic": MessageLookupByLibrary.simpleMessage("YTMusic"),
    "Yes": MessageLookupByLibrary.simpleMessage("Yes"),
    "nSongs": m0,
  };
}
