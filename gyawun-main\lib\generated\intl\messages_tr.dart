// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a tr locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'tr';

  static String m0(count) =>
      "${Intl.plural(count, zero: '<PERSON>arkı Yok', one: '1 Şarkı', other: '${count} Şarkı')}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "About": MessageLookupByLibrary.simpleMessage("Hakkında"),
    "Add_To_Favourites": MessageLookupByLibrary.simpleMessage(
      "Favorilere Ekle",
    ),
    "Add_To_Library": MessageLookupByLibrary.simpleMessage("Kütüphaneye Ekle"),
    "Add_To_Playlist": MessageLookupByLibrary.simpleMessage(
      "Çalma Listesine Ekle",
    ),
    "Add_To_Queue": MessageLookupByLibrary.simpleMessage("Kuyruğa Ekle"),
    "Album": MessageLookupByLibrary.simpleMessage("Albüm"),
    "Albums": MessageLookupByLibrary.simpleMessage("Albümler"),
    "Appearence": MessageLookupByLibrary.simpleMessage("Görünüm"),
    "Artists": MessageLookupByLibrary.simpleMessage("Sanatçılar"),
    "Audio_And_Playback": MessageLookupByLibrary.simpleMessage(
      "Ses ve Yeniden Oynatma",
    ),
    "Backup": MessageLookupByLibrary.simpleMessage("Yedekle"),
    "Backup_And_Restore": MessageLookupByLibrary.simpleMessage(
      "Yedekle ve Geri Yükle",
    ),
    "Battery_Optimisation_message": MessageLookupByLibrary.simpleMessage(
      "Gyawun un düzgün çalışması için pil optimizasyonunu devre dışı bırakmak için buraya tıklayın",
    ),
    "Battery_Optimisation_title": MessageLookupByLibrary.simpleMessage(
      "Pil Optimizasyonu Tespit Edildi",
    ),
    "Bug_Report": MessageLookupByLibrary.simpleMessage("Hata Raporu"),
    "Buy_Me_A_Coffee": MessageLookupByLibrary.simpleMessage(
      "Bana Bir Kahve Al",
    ),
    "Cancel": MessageLookupByLibrary.simpleMessage("İptal"),
    "Check_For_Update": MessageLookupByLibrary.simpleMessage(
      "Güncellemeleri Kontrol Et",
    ),
    "Confirm": MessageLookupByLibrary.simpleMessage("Onayla"),
    "Content": MessageLookupByLibrary.simpleMessage("İçerik"),
    "Contributors": MessageLookupByLibrary.simpleMessage("Katkıda Bulunanlar"),
    "Copied_To_Clipboard": MessageLookupByLibrary.simpleMessage(
      "Panoya Kopyalandı",
    ),
    "Country": MessageLookupByLibrary.simpleMessage("Ülke"),
    "Create": MessageLookupByLibrary.simpleMessage("Oluştur"),
    "Create_Playlist": MessageLookupByLibrary.simpleMessage(
      "Çalma Listesi Oluştur",
    ),
    "DOwnload_Quality": MessageLookupByLibrary.simpleMessage(
      "İndirme Kalitesi",
    ),
    "Delete_Item_Message": MessageLookupByLibrary.simpleMessage(
      "Bu öğeyi silmek istediğinizden emin misiniz?",
    ),
    "Delete_Playback_History": MessageLookupByLibrary.simpleMessage(
      "Yeniden Oynatma Geçmişini Sil",
    ),
    "Delete_Playback_History_Confirm_Message":
        MessageLookupByLibrary.simpleMessage(
          "Yeniden Oynatma Geçmişini silmek istediğinizden emin misiniz.",
        ),
    "Delete_Search_History": MessageLookupByLibrary.simpleMessage(
      "Arama Geçmişini Sil",
    ),
    "Delete_Search_History_Confirm_Message":
        MessageLookupByLibrary.simpleMessage(
          "Arama Geçmişini silmek istediğinizden emin misiniz.",
        ),
    "Developer": MessageLookupByLibrary.simpleMessage("Geliştirici"),
    "Donate": MessageLookupByLibrary.simpleMessage("Bağış Yap"),
    "Donate_Message": MessageLookupByLibrary.simpleMessage(
      "Gyawun un geliştirilmesini destekleyin",
    ),
    "Done": MessageLookupByLibrary.simpleMessage("Tamam"),
    "Download": MessageLookupByLibrary.simpleMessage("İndir"),
    "Downloads": MessageLookupByLibrary.simpleMessage("İndirilenler"),
    "Dynamic_Colors": MessageLookupByLibrary.simpleMessage("Dinamik Renkler"),
    "Enable_Equalizer": MessageLookupByLibrary.simpleMessage(
      "Ekolayzeri Etkinleştir",
    ),
    "Enable_Playback_History": MessageLookupByLibrary.simpleMessage(
      "Yeniden Oynatma Geçmişini Etkinleştir",
    ),
    "Enable_Search_History": MessageLookupByLibrary.simpleMessage(
      "Arama Geçmişini Etkinleştir",
    ),
    "Enter_Visitor_Id": MessageLookupByLibrary.simpleMessage(
      "Ziyaretçi Kimliği Girin",
    ),
    "Equalizer": MessageLookupByLibrary.simpleMessage("Ekolayzer"),
    "Favourites": MessageLookupByLibrary.simpleMessage("Favoriler"),
    "Feature_Request": MessageLookupByLibrary.simpleMessage("Özellik İsteği"),
    "Google_Account": MessageLookupByLibrary.simpleMessage("Google Hesabı"),
    "Gyawun": MessageLookupByLibrary.simpleMessage("Gyawun"),
    "High": MessageLookupByLibrary.simpleMessage("Yüksek"),
    "History": MessageLookupByLibrary.simpleMessage("Geçmiş"),
    "Home": MessageLookupByLibrary.simpleMessage("Ana Sayfa"),
    "Import": MessageLookupByLibrary.simpleMessage("İçe Aktar"),
    "Import_Playlist": MessageLookupByLibrary.simpleMessage(
      "Çalma Listesi İçe Aktar",
    ),
    "Jhelum_Corp": MessageLookupByLibrary.simpleMessage("Jhelum Corp"),
    "Language": MessageLookupByLibrary.simpleMessage("Dil"),
    "Loudness_And_Equalizer": MessageLookupByLibrary.simpleMessage(
      "Ses Yüksekliği ve Ekolayzer",
    ),
    "Loudness_Enhancer": MessageLookupByLibrary.simpleMessage("Ses Yükseltici"),
    "Low": MessageLookupByLibrary.simpleMessage("Düşük"),
    "Made_In_Kashmir": MessageLookupByLibrary.simpleMessage(
      "Keşmir de Yapıldı",
    ),
    "Name": MessageLookupByLibrary.simpleMessage("İsim"),
    "Next_Up": MessageLookupByLibrary.simpleMessage("Sıradaki"),
    "No": MessageLookupByLibrary.simpleMessage("Hayır"),
    "Organisation": MessageLookupByLibrary.simpleMessage("Organizasyon"),
    "Pay_With_UPI": MessageLookupByLibrary.simpleMessage("UPI ile Öde"),
    "Payment_Methods": MessageLookupByLibrary.simpleMessage("Ödeme Yöntemleri"),
    "Personalised_Content": MessageLookupByLibrary.simpleMessage(
      "Kişiselleştirilmiş İçerik",
    ),
    "Play_Next": MessageLookupByLibrary.simpleMessage("Sonraki Oynat"),
    "Playback_History_Deleted": MessageLookupByLibrary.simpleMessage(
      "Yeniden Oynatma Geçmişi Silindi",
    ),
    "Playlist_Name": MessageLookupByLibrary.simpleMessage("Çalma Listesi Adı"),
    "Playlists": MessageLookupByLibrary.simpleMessage("Çalma Listeleri"),
    "Progress": MessageLookupByLibrary.simpleMessage("İlerleme"),
    "Remove": MessageLookupByLibrary.simpleMessage("Kaldır"),
    "Remove_All_History_Message": MessageLookupByLibrary.simpleMessage(
      "Tüm geçmişi temizlemek istediğinizden emin misiniz?",
    ),
    "Remove_From_Favourites": MessageLookupByLibrary.simpleMessage(
      "Favorilerden Çıkar",
    ),
    "Remove_From_Library": MessageLookupByLibrary.simpleMessage(
      "Kütüphaneden Çıkar",
    ),
    "Remove_From_YTMusic_Message": MessageLookupByLibrary.simpleMessage(
      "YTMüzikten kaldırmak istediğinizden emin misiniz?",
    ),
    "Remove_Message": MessageLookupByLibrary.simpleMessage(
      "Kaldırmak istediğinizden emin misiniz?",
    ),
    "Rename": MessageLookupByLibrary.simpleMessage("Yeniden Adlandır"),
    "Rename_Playlist": MessageLookupByLibrary.simpleMessage(
      "Çalma Listesini Yeniden Adlandır",
    ),
    "Reset_Visitor_Id": MessageLookupByLibrary.simpleMessage(
      "Ziyaretçi Kimliğini Sıfırla",
    ),
    "Restore": MessageLookupByLibrary.simpleMessage("Geri Yükle"),
    "Saved": MessageLookupByLibrary.simpleMessage("Kaydedilenler"),
    "Search_Gyawun": MessageLookupByLibrary.simpleMessage("Gyawun Ara"),
    "Search_History_Deleted": MessageLookupByLibrary.simpleMessage(
      "Arama Geçmişi Silindi",
    ),
    "Search_Settings": MessageLookupByLibrary.simpleMessage("Arama Ayarları"),
    "Select_Backup": MessageLookupByLibrary.simpleMessage("Yedeği Seç"),
    "Settings": MessageLookupByLibrary.simpleMessage("Ayarlar"),
    "Sheikh_Haziq": MessageLookupByLibrary.simpleMessage("Sheikh Haziq"),
    "Show_Less": MessageLookupByLibrary.simpleMessage("Daha Az Göster"),
    "Show_More": MessageLookupByLibrary.simpleMessage("Daha Fazla Göster"),
    "Shuffle": MessageLookupByLibrary.simpleMessage("Karıştır"),
    "Skip_Silence": MessageLookupByLibrary.simpleMessage("Sessizliği Atla"),
    "Sleep_Timer": MessageLookupByLibrary.simpleMessage("Uyku Zamanlayıcı"),
    "Songs": MessageLookupByLibrary.simpleMessage("Şarkılar"),
    "Songs_Will_Start_Playing_Soon": MessageLookupByLibrary.simpleMessage(
      "Şarkılar yakında çalmaya başlayacak.",
    ),
    "Source_Code": MessageLookupByLibrary.simpleMessage("Kaynak Kodu"),
    "Start_Radio": MessageLookupByLibrary.simpleMessage("Radyo Başlat"),
    "Streaming_Quality": MessageLookupByLibrary.simpleMessage("Yayın Kalitesi"),
    "Subscriptions": MessageLookupByLibrary.simpleMessage("Abonelikler"),
    "Support_Me_On_Kofi": MessageLookupByLibrary.simpleMessage(
      "Ko-fi üzerinden bana destek ol",
    ),
    "Telegram": MessageLookupByLibrary.simpleMessage("Telegram"),
    "Theme_Mode": MessageLookupByLibrary.simpleMessage("Tema Modu"),
    "Version": MessageLookupByLibrary.simpleMessage("Versiyon"),
    "Visitor_Id": MessageLookupByLibrary.simpleMessage("Ziyaretçi Kimliği"),
    "Window_Effect": MessageLookupByLibrary.simpleMessage("Pencere Efekti"),
    "YTMusic": MessageLookupByLibrary.simpleMessage("YTMüzik"),
    "Yes": MessageLookupByLibrary.simpleMessage("Evet"),
    "nSongs": m0,
  };
}
