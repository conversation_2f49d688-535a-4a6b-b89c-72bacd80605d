
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';

import '../../generated/l10n.dart';
import '../../utils/adaptive_widgets/adaptive_widgets.dart';
import '../../widgets/ios_style_card.dart';
import '../../widgets/ios_style_search_bar.dart';
import '../../widgets/ios_style_transitions.dart';
import '../../ytmusic/ytmusic.dart';
import 'section_item.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with SingleTickerProviderStateMixin {
  final YTMusic ytMusic = GetIt.I<YTMusic>();
  late ScrollController _scrollController;
  late List chips = [];
  late List sections = [];
  int page = 0;
  String? continuation;
  bool initialLoading = true;
  bool nextLoading = false;
  late AnimationController _sectionController;
  late Animation<double> _sectionFade;
  late Animation<Offset> _sectionSlide;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);
    fetchHome();
    _sectionController = AnimationController(
      duration: const Duration(milliseconds: 900),
      vsync: this,
    );
    _sectionFade = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(
      parent: _sectionController,
      curve: Curves.easeIn,
    ));
    _sectionSlide = Tween<Offset>(begin: const Offset(0, 0.08), end: Offset.zero).animate(CurvedAnimation(
      parent: _sectionController,
      curve: Curves.easeOutCubic,
    ));
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _sectionController.forward();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _sectionController.dispose();
    super.dispose();
  }

  _scrollListener() async {
    if (initialLoading || nextLoading || continuation == null) {
      return;
    }

    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      await fetchNext();
    }
  }

  fetchHome() async {
    setState(() {
      initialLoading = true;
      nextLoading = false;
    });

    try {
      debugPrint('🏠 HomeScreen: Starting to fetch home data...');
      Map<String, dynamic> home = await ytMusic.browse();
      debugPrint('🏠 HomeScreen: Received home data: ${home.keys}');

      if (mounted) {
        setState(() {
          initialLoading = false;
          nextLoading = false;

          // Safely extract chips
          if (home['chips'] is List) {
            chips = List.from(home['chips']);
          } else {
            chips = [];
          }

          // Safely extract sections
          if (home['sections'] is List) {
            sections = List.from(home['sections']);
          } else {
            sections = [];
          }

          continuation = home['continuation'];
        });
        debugPrint('🏠 HomeScreen: Updated state - chips: ${chips.length}, sections: ${sections.length}');
      }
    } catch (e, stackTrace) {
      debugPrint('❌ HomeScreen: Error fetching home data: $e');
      debugPrint('❌ HomeScreen: Stack trace: $stackTrace');

      if (mounted) {
        setState(() {
          initialLoading = false;
          nextLoading = false;
          chips = [];
          sections = [];
          continuation = null;
        });
      }
    }
  }

  refresh() async {
    if (initialLoading) return;

    try {
      debugPrint('🔄 HomeScreen: Refreshing home data...');
      Map<String, dynamic> home = await ytMusic.browse();

      if (mounted) {
        setState(() {
          initialLoading = false;
          nextLoading = false;

          // Safely extract chips
          if (home['chips'] is List) {
            chips = List.from(home['chips']);
          } else {
            chips = [];
          }

          // Safely extract sections
          if (home['sections'] is List) {
            sections = List.from(home['sections']);
          } else {
            sections = [];
          }

          continuation = home['continuation'];
        });
        debugPrint('🔄 HomeScreen: Refresh completed successfully');
      }
    } catch (e) {
      debugPrint('❌ HomeScreen: Error refreshing home data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to refresh: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  fetchNext() async {
    if (continuation == null) return;
    setState(() {
      nextLoading = true;
    });
    Map<String, dynamic> home =
        await ytMusic.browseContinuation(additionalParams: continuation!);
    List<Map<String, dynamic>> secs =
        home['sections'].cast<Map<String, dynamic>>();
    if (mounted) {
      setState(() {
        sections.addAll(secs);
        continuation = home['continuation'];
        nextLoading = false;
      });
    }
  }

  Widget _horizontalChipsRow(List data) {
    var list = <Widget>[const SizedBox(width: 16)];
    for (var element in data) {
      list.add(
        AdaptiveInkWell(
          borderRadius: BorderRadius.circular(25),
          onTap: () => context.go('/chip', extra: element),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: Theme.of(context).brightness == Brightness.dark
                    ? [
                        const Color(0xFF2A2A2A),
                        const Color(0xFF3A3A3A),
                      ]
                    : [
                        Colors.white,
                        const Color(0xFFF8F9FA),
                      ],
              ),
              borderRadius: BorderRadius.circular(25),
              border: Border.all(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white.withValues(alpha: 0.1)
                    : Colors.black.withValues(alpha: 0.08),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.black.withValues(alpha: 0.4)
                      : Colors.black.withValues(alpha: 0.08),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Text(
              element['title'],
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black,
              ),
            ),
          ),
        ),
      );
      list.add(const SizedBox(width: 12));
    }
    list.add(const SizedBox(width: 8));
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(
          parent: AlwaysScrollableScrollPhysics(),
        ), // Enhanced smooth scrolling
        clipBehavior: Clip.none, // Optimize clipping
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: list,
        ),
      ),
    );
  }

  String _getTimeBasedGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return "Good morning";
    } else if (hour < 17) {
      return "Good afternoon";
    } else {
      return "Good evening";
    }
  }

  Widget _buildGreeting() {
    final box = Hive.box('SETTINGS');
    final username = box.get('username', defaultValue: 'Music Lover');

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: Theme.of(context).brightness == Brightness.dark
              ? [
                  const Color(0xFF0A0A0A),
                  const Color(0xFF1A1A1A),
                  const Color(0xFF2A2A2A),
                  iOSBlue.withValues(alpha: 0.2),
                ]
              : [
                  Colors.white,
                  const Color(0xFFF8F9FA),
                  iOSBlue.withValues(alpha: 0.08),
                  iOSPurple.withValues(alpha: 0.12),
                ],
          stops: const [0.0, 0.3, 0.7, 1.0],
        ),
        borderRadius: BorderRadius.circular(iOSCardRadius),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.white.withValues(alpha: 0.15)
              : iOSBlue.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.6)
                : iOSBlue.withValues(alpha: 0.15),
            blurRadius: 30,
            offset: const Offset(0, 12),
            spreadRadius: 3,
          ),
          if (Theme.of(context).brightness == Brightness.dark) ...[
            BoxShadow(
              color: iOSBlue.withValues(alpha: 0.4),
              blurRadius: 40,
              offset: const Offset(0, 0),
              spreadRadius: -8,
            ),
            BoxShadow(
              color: iOSPurple.withValues(alpha: 0.3),
              blurRadius: 50,
              offset: const Offset(0, 0),
              spreadRadius: -12,
            ),
          ] else ...[
            BoxShadow(
              color: iOSPurple.withValues(alpha: 0.1),
              blurRadius: 25,
              offset: const Offset(0, 6),
              spreadRadius: -2,
            ),
          ],
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [iOSBlue, iOSPurple],
              ),
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: iOSBlue.withValues(alpha: 0.4),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: const Icon(
              Icons.waving_hand,
              color: Colors.white,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${_getTimeBasedGreeting()}!",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.white.withValues(alpha: 0.9)
                        : Colors.black.withValues(alpha: 0.8),
                    letterSpacing: 0.2,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  "Welcome back, $username",
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w800,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black,
                    letterSpacing: -0.5,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final List<Map> quickPicks = [];
    try {
      if (sections.isNotEmpty &&
          sections[0] != null &&
          sections[0] is Map &&
          sections[0]['contents'] is List) {
        final contents = sections[0]['contents'] as List;
        for (var item in contents.take(8)) {
          if (item != null && item is Map) {
            quickPicks.add(Map<String, dynamic>.from(item));
          }
        }
      }
    } catch (e) {
      debugPrint('❌ HomeScreen: Error extracting quickPicks: $e');
    }
    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        CupertinoSliverRefreshControl(
          onRefresh: () async => await refresh(),
        ),
        SliverToBoxAdapter(
          child: SafeArea(
            child: FadeTransition(
              opacity: _sectionFade,
              child: SlideTransition(
                position: _sectionSlide,
                child: sections.isEmpty && chips.isEmpty
                    ? _buildEmptyState()
                    : Column(
                        children: [
                          _horizontalChipsRow(chips),
                          _buildGreeting(),
                          _buildQuickPicks(quickPicks),
                          _buildAlbumsForYou(),
                          _buildTopMusicVideos(),
                          _buildRealTimeMusicSuggestion(),
                          Column(
                            children: [
                              ...sections.asMap().entries.where((entry) => entry.value != null).map((entry) {
                                int index = entry.key;
                                Map section = entry.value;
                                return RepaintBoundary(
                                  key: ValueKey('section_$index'),
                                  child: SectionItem(section: section),
                                );
                              }),
                              if (!nextLoading && continuation != null)
                                const SizedBox(height: 50),
                              if (nextLoading)
                                const Padding(
                                    padding: EdgeInsets.all(8.0),
                                    child: AdaptiveProgressRing()),
                            ],
                          )
                        ],
                      ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    iOSBlue.withValues(alpha: 0.3),
                    iOSPurple.withValues(alpha: 0.3),
                  ],
                ),
                borderRadius: BorderRadius.circular(60),
              ),
              child: const Icon(
                Icons.music_note_rounded,
                size: 60,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 32),
            Text(
              "Welcome to CODO Music",
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w700,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              "Unable to load music content.\nPlease check your internet connection and try again.",
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white.withValues(alpha: 0.7)
                    : Colors.black.withValues(alpha: 0.6),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [iOSBlue, iOSPurple],
                ),
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: iOSBlue.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(25),
                  onTap: () => fetchHome(),
                  child: const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.refresh, color: Colors.white),
                        SizedBox(width: 8),
                        Text(
                          "Retry",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickPicks(List<Map> quickPicks) {
    if (quickPicks.isEmpty) return const SizedBox.shrink();
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Enhanced iOS-style Header
          Container(
            padding: const EdgeInsets.all(20),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  const Color(0xFFEAF6FF), // Soft blue
                  const Color(0xFFF3E8FF), // Soft lavender
                  Colors.white.withValues(alpha: 0.95),
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.06),
                  blurRadius: 18,
                  offset: const Offset(0, 6),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Quick Picks",
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.w700,
                        fontFamily: '.SF Pro Display', // iOS system font if available
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.white
                            : Colors.black,
                        letterSpacing: -0.5,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      "Your personalized music selection",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        fontFamily: '.SF Pro Text',
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.grey[400]
                            : Colors.grey[600],
                        letterSpacing: -0.2,
                      ),
                    ),
                  ],
                ),

              ],
            ),
          ),
          // Enhanced iOS-style Music Grid
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.symmetric(horizontal: 4),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: 0.8, // Original ratio
            ),
            itemCount: quickPicks.length > 10 ? 10 : quickPicks.length,
            itemBuilder: (context, index) {
              final song = quickPicks[index];
              return _buildEnhancediOSMusicCard(song, quickPicks);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancediOSMusicCard(Map song, List<Map> allQuickPicks) {
    return _AnimatedMusicCard(
      onTap: () async {
        HapticFeedback.lightImpact();
        try {
          List<Map<String, dynamic>> validSongs = [];
          for (var songItem in allQuickPicks) {
            if (songItem is Map) {
              Map<String, dynamic> safeSong = {};
              songItem.forEach((key, value) {
                if (key != null && value != null) {
                  safeSong[key.toString()] = value;
                }
              });
              if (safeSong['videoId'] != null && safeSong['videoId'].toString().isNotEmpty) {
                validSongs.add(safeSong);
              }
            }
          }
          if (validSongs.isNotEmpty) {
            await GetIt.I<MediaPlayer>().playAllSongs(validSongs);
          }
        } catch (e) {
          debugPrint('❌ EnhancediOSMusicCard: Error playing song: $e');
        }
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(28),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
          child: Container(
            height: 260,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(28),
              color: Colors.white.withValues(alpha: 0.85),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.08),
                  blurRadius: 24,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(28),
                onTap: () async {
                  HapticFeedback.lightImpact();
                  try {
                    List<Map<String, dynamic>> validSongs = [];
                    for (var songItem in allQuickPicks) {
                      if (songItem is Map) {
                        Map<String, dynamic> safeSong = {};
                        songItem.forEach((key, value) {
                          if (key != null && value != null) {
                            safeSong[key.toString()] = value;
                          }
                        });
                        if (safeSong['videoId'] != null && safeSong['videoId'].toString().isNotEmpty) {
                          validSongs.add(safeSong);
                        }
                      }
                    }
                    if (validSongs.isNotEmpty) {
                      await GetIt.I<MediaPlayer>().playAllSongs(validSongs);
                    }
                  } catch (e) {
                    debugPrint('❌ EnhancediOSMusicCard: Error playing song: $e');
                  }
                },
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Enhanced Album Art with iOS-style design
                    Expanded(
                      flex: 2,
                      child: SizedBox(
                        width: double.infinity,
                        child: ClipRRect(
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(24),
                            topRight: Radius.circular(24),
                          ),
                          child: Stack(
                            children: [
                              // Album Art
                              song['thumbnails'] != null && song['thumbnails'].isNotEmpty
                                  ? Image.network(
                                      _getEnhancedImageUrl(song['thumbnails']),
                                      fit: BoxFit.cover,
                                      width: double.infinity,
                                      height: double.infinity,
                                      errorBuilder: (context, error, stackTrace) {
                                        return Container(
                                          decoration: BoxDecoration(
                                            gradient: LinearGradient(
                                              begin: Alignment.topLeft,
                                              end: Alignment.bottomRight,
                                              colors: [
                                                const Color(0xFF007AFF).withValues(alpha: 0.3),
                                                const Color(0xFF5856D6).withValues(alpha: 0.3),
                                              ],
                                            ),
                                          ),
                                          child: const Icon(
                                            Icons.music_note_rounded,
                                            size: 48,
                                            color: Colors.white,
                                          ),
                                        );
                                      },
                                    )
                                  : Container(
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors: [
                                            const Color(0xFF007AFF).withValues(alpha: 0.3),
                                            const Color(0xFF5856D6).withValues(alpha: 0.3),
                                          ],
                                        ),
                                      ),
                                      child: const Icon(
                                        Icons.music_note_rounded,
                                        size: 48,
                                        color: Colors.white,
                                      ),
                                    ),
                              // iOS-style play overlay
                              Positioned(
                                bottom: 12,
                                right: 12,
                                child: Container(
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: Colors.black.withValues(alpha: 0.8),
                                    borderRadius: BorderRadius.circular(25),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withValues(alpha: 0.3),
                                        blurRadius: 8,
                                        offset: const Offset(0, 4),
                                      ),
                                    ],
                                  ),
                                  child: const Icon(
                                    Icons.play_arrow_rounded,
                                    color: Colors.white,
                                    size: 24,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    // Enhanced Song Info Section
                    Expanded(
                      flex: 3,
                      child: Padding(
                        padding: const EdgeInsets.all(18),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Song Title with iOS typography
                            Flexible(
                              child: Text(
                                song['title'] ?? 'Unknown Title',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Colors.white
                                      : Colors.black,
                                  letterSpacing: -0.4,
                                  height: 1.2,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(height: 6),
                            // Artist with iOS secondary text style
                            Text(
                              song['subtitle'] ?? song['artist'] ?? 'Unknown Artist',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: Theme.of(context).brightness == Brightness.dark
                                    ? const Color(0xFF8E8E93)
                                    : const Color(0xFF6D6D70),
                                letterSpacing: -0.2,
                                height: 1.1,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAlbumsForYou() {
    // Get album data from sections (typically section 1 or 2 contains albums)
    List<Map> albums = [];
    try {
      for (var section in sections) {
        if (section != null &&
            section is Map &&
            section['title'] != null &&
            (section['title'].toString().toLowerCase().contains('album') ||
             section['title'].toString().toLowerCase().contains('for you') ||
             section['title'].toString().toLowerCase().contains('recommended'))) {
          if (section['contents'] is List) {
            final contents = section['contents'] as List;
            for (var item in contents.take(6)) {
              if (item != null && item is Map) {
                albums.add(Map<String, dynamic>.from(item));
              }
            }
            break;
          }
        }
      }
      // Fallback: use any section with album-like content
      if (albums.isEmpty && sections.length > 1 && sections[1] != null) {
        var fallbackSection = sections[1];
        if (fallbackSection is Map && fallbackSection['contents'] is List) {
          final contents = fallbackSection['contents'] as List;
          for (var item in contents.take(6)) {
            if (item != null && item is Map) {
              albums.add(Map<String, dynamic>.from(item));
            }
          }
        }
      }
    } catch (e) {
      debugPrint('❌ HomeScreen: Error extracting albums: $e');
    }

    if (albums.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
            child: Text(
              "Albums for you",
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w800,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black,
              ),
            ),
          ),
          // Albums Horizontal Scroll
          SizedBox(
            height: 220, // Original height
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 4),
              itemCount: albums.length > 10 ? 10 : albums.length,
              itemBuilder: (context, index) {
                final album = albums[index];
                return _buildAlbumCard(album);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAlbumCard(Map album) {
    return _AnimatedMusicCard(
      onTap: () {
        HapticFeedback.mediumImpact();
        if (album['endpoint'] != null) {
          debugPrint('🎵 Album tapped: ${album['title']}');
        }
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
          child: Container(
            width: 160, // Original size
            margin: const EdgeInsets.only(right: 20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: Colors.white.withValues(alpha: 0.85),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.10),
                  blurRadius: 18,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Album Cover
                  Container(
                    width: 160, // Original size
                    height: 160, // Original size
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.18),
                          blurRadius: 18,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: album['thumbnails'] != null && album['thumbnails'].isNotEmpty
                          ? Image.network(
                              _getEnhancedImageUrl(album['thumbnails']),
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  decoration: BoxDecoration(
                                    color: Colors.grey[300],
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: const Icon(
                                    Icons.album,
                                    size: 60,
                                    color: Colors.grey,
                                  ),
                                );
                              },
                            )
                          : Container(
                              decoration: BoxDecoration(
                                color: Colors.grey[300],
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: const Icon(
                                Icons.album,
                                size: 60,
                                color: Colors.grey,
                              ),
                            ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  // Album Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          album['title']?.toString() ?? 'Unknown Album',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 18,
                            fontFamily: '.SF Pro Display',
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.white
                                : Colors.black,
                            height: 1.2,
                            letterSpacing: -0.3,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 6),
                        Text(
                          album['subtitle']?.toString() ?? 'Various Artists',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            fontFamily: '.SF Pro Text',
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.white.withValues(alpha: 0.7)
                                : Colors.black.withValues(alpha: 0.6),
                            height: 1.1,
                            letterSpacing: -0.2,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTopMusicVideos() {
    // Get music videos from sections
    List<Map> videos = [];
    try {
      for (var section in sections) {
        if (section != null &&
            section is Map &&
            section['title'] != null &&
            (section['title'].toString().toLowerCase().contains('video') ||
             section['title'].toString().toLowerCase().contains('music video') ||
             section['title'].toString().toLowerCase().contains('trending'))) {
          if (section['contents'] is List) {
            final contents = section['contents'] as List;
            for (var item in contents.take(4)) {
              if (item != null && item is Map) {
                videos.add(Map<String, dynamic>.from(item));
              }
            }
            break;
          }
        }
      }
      // Fallback: use any section with video-like content
      if (videos.isEmpty && sections.length > 2 && sections[2] != null) {
        var fallbackSection = sections[2];
        if (fallbackSection is Map && fallbackSection['contents'] is List) {
          final contents = fallbackSection['contents'] as List;
          for (var item in contents.take(4)) {
            if (item != null && item is Map) {
              videos.add(Map<String, dynamic>.from(item));
            }
          }
        }
      }
    } catch (e) {
      debugPrint('❌ HomeScreen: Error extracting videos: $e');
    }

    if (videos.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header with More button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Top music videos",
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w800,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black,
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.white.withValues(alpha: 0.1)
                        : Colors.black.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(20),
                      onTap: () {
                        // Navigate to more videos
                        debugPrint('🎵 More videos tapped');
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        child: Text(
                          "More",
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.white
                                : Colors.black,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Videos List
          Column(
            children: videos.take(2).map((video) => _buildVideoTile(video)).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoTile(Map video) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () async {
            try {
              if (video['videoId'] != null) {
                Map<String, dynamic> validVideo = Map<String, dynamic>.from(video);
                await GetIt.I<MediaPlayer>().playSong(validVideo);
              }
            } catch (e) {
              debugPrint('❌ VideoTile: Error playing video: $e');
            }
          },
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey[900]?.withValues(alpha: 0.6)
                  : Colors.grey[100]?.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Video Thumbnail
                Container(
                  width: 84,
                  height: 64,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Stack(
                      children: [
                        if (video['thumbnails'] != null && video['thumbnails'].isNotEmpty)
                          Image.network(
                            _getEnhancedImageUrl(video['thumbnails']),
                            width: 80,
                            height: 60,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: Colors.grey[300],
                                child: const Icon(Icons.video_library, color: Colors.grey),
                              );
                            },
                          )
                        else
                          Container(
                            color: Colors.grey[300],
                            child: const Icon(Icons.video_library, color: Colors.grey),
                          ),
                        // Play overlay
                        Center(
                          child: Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: Colors.black.withValues(alpha: 0.6),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.play_arrow,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                // Video Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        video['title']?.toString() ?? 'Unknown Video',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        video['subtitle']?.toString() ?? 'Unknown Artist',
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withValues(alpha: 0.7)
                              : Colors.black.withValues(alpha: 0.6),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRealTimeMusicSuggestion() {
    if (sections.isEmpty) return const SizedBox.shrink();

    // Get trending/recommended songs from the first section
    final suggestedSongs = <Map>[];
    try {
      if (sections.isNotEmpty &&
          sections[0] != null &&
          sections[0] is Map &&
          sections[0]['contents'] != null &&
          sections[0]['contents'] is List) {

        final contents = sections[0]['contents'] as List;
        for (var item in contents.take(5)) {
          if (item != null && item is Map) {
            suggestedSongs.add(item);
          }
        }
      }
    } catch (e) {
      debugPrint('❌ HomeScreen: Error extracting suggested songs: $e');
    }

    if (suggestedSongs.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: Theme.of(context).brightness == Brightness.dark
              ? [
                  const Color(0xFF1DB954).withValues(alpha: 0.9),
                  const Color(0xFF1ED760).withValues(alpha: 0.8),
                  const Color(0xFF0E4B1C).withValues(alpha: 0.9),
                ]
              : [
                  const Color(0xFF1DB954).withValues(alpha: 0.1),
                  const Color(0xFF1ED760).withValues(alpha: 0.08),
                  const Color(0xFFE8F5E8).withValues(alpha: 0.9),
                ],
          stops: const [0.0, 0.5, 1.0],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF1DB954).withValues(alpha: 0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF1DB954).withValues(alpha: 0.4),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 2,
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ConstrainedBox(
        constraints: const BoxConstraints(minHeight: 400),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with animation
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.trending_up_rounded,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : const Color(0xFF1DB954),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "🔥 Trending Now",
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w800,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : const Color(0xFF1DB954),
                          letterSpacing: -0.3,
                        ),
                      ),
                      Text(
                        "Real-time music suggestions",
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withValues(alpha: 0.8)
                              : const Color(0xFF1DB954).withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Text(
                    "LIVE",
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w700,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : const Color(0xFF1DB954),
                      letterSpacing: 0.5,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // List-style music suggestions with curved edges
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.08),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.15),
                width: 1,
              ),
            ),
            child: Column(
              children: suggestedSongs.asMap().entries.map((entry) {
                int index = entry.key;
                Map song = entry.value;
                return _buildListStyleSongCard(song, index, suggestedSongs.length);
              }).toList(),
            ),
          ),

          // Play All Button
          Padding(
            padding: const EdgeInsets.all(20),
            child: SizedBox(
              width: double.infinity,
              child: CupertinoButton.filled(
                borderRadius: BorderRadius.circular(20),
                padding: const EdgeInsets.symmetric(vertical: 18),
                onPressed: () async {
                  HapticFeedback.mediumImpact();
                  try {
                    if (suggestedSongs.isNotEmpty) {
                      List<Map<String, dynamic>> validSongs = [];
                      for (var song in suggestedSongs) {
                        Map<String, dynamic> safeSong = {};
                        song.forEach((key, value) {
                          if (key != null) {
                            safeSong[key.toString()] = value;
                          }
                        });
                        if (safeSong['videoId'] != null && safeSong['videoId'].toString().trim().isNotEmpty) {
                          validSongs.add(safeSong);
                        }
                      }
                      if (validSongs.isNotEmpty) {
                        await GetIt.I<MediaPlayer>().playAllSongs(validSongs);
                      }
                    }
                  } catch (e) {
                    debugPrint('❌ HomeScreen: Error in Play All button: $e');
                  }
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.play_arrow_rounded,
                      size: 26,
                      color: Colors.white,
                    ),
                    const SizedBox(width: 10),
                    Text(
                      "Play All Trending",
                      style: TextStyle(
                        fontSize: 17,
                        fontWeight: FontWeight.w800,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
        ),
      ),
    );
  }

  Widget _buildListStyleSongCard(Map song, int index, int totalCount) {
    final thumbnails = song['thumbnails'] as List?;
    final imageUrl = _getEnhancedImageUrl(thumbnails);

    final artists = song['artists'] as List?;
    final artistNames = artists?.isNotEmpty == true
        ? artists!.map((artist) => artist['name']?.toString() ?? 'Unknown Artist').join(', ')
        : 'Unknown Artist';

    final isFirst = index == 0;
    final isLast = index == totalCount - 1;

    return Container(
      margin: EdgeInsets.only(
        top: isFirst ? 0 : 0,
        bottom: isLast ? 0 : 1,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(isFirst ? 20 : 0),
          topRight: Radius.circular(isFirst ? 20 : 0),
          bottomLeft: Radius.circular(isLast ? 20 : 0),
          bottomRight: Radius.circular(isLast ? 20 : 0),
        ),
        color: Colors.white.withValues(alpha: 0.85),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(isFirst ? 20 : 0),
            topRight: Radius.circular(isFirst ? 20 : 0),
            bottomLeft: Radius.circular(isLast ? 20 : 0),
            bottomRight: Radius.circular(isLast ? 20 : 0),
          ),
          splashColor: const Color(0xFF1DB954).withValues(alpha: 0.2),
          highlightColor: const Color(0xFF1DB954).withValues(alpha: 0.1),
          onTap: () async {
            HapticFeedback.lightImpact();
            try {
              // Ensure song is properly typed
              Map<String, dynamic> safeSong = {};
              song.forEach((key, value) {
                if (key != null && value != null) {
                  safeSong[key.toString()] = value;
                }
              });
              await GetIt.I<MediaPlayer>().playSong(safeSong);
            } catch (e) {
              debugPrint('Error playing song: $e');
            }
          },
          child: Row(
            children: [
              // Ranking badge
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xFF1DB954),
                      const Color(0xFF1ED760),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF1DB954).withValues(alpha: 0.3),
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Center(
                  child: Text(
                    '${index + 1}',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w800,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // Album Art
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.network(
                    imageUrl,
                    fit: BoxFit.cover,
                    cacheWidth: 180, // Higher quality cache
                    cacheHeight: 180,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Center(
                          child: SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                        ),
                      );
                    },
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.music_note_rounded,
                          color: Colors.white.withValues(alpha: 0.7),
                          size: 24,
                        ),
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // Song details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Flexible(
                      child: Text(
                        song['title']?.toString() ?? 'Unknown Song',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black,
                          height: 1.1,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Flexible(
                      child: Text(
                        artistNames,
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w400,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withValues(alpha: 0.7)
                              : Colors.black.withValues(alpha: 0.6),
                          height: 1.1,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              // Play icon
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: const Color(0xFF1DB954).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.play_arrow_rounded,
                  color: const Color(0xFF1DB954),
                  size: 24,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getEnhancedImageUrl(List? thumbnails) {
    try {
      if (thumbnails == null || thumbnails.isEmpty) {
        return 'https://via.placeholder.com/120x120';
      }

      // Find the highest quality thumbnail available
      var validThumbnails = thumbnails.where((el) =>
        el != null &&
        el is Map &&
        el['width'] != null &&
        el['url'] != null
      ).toList();

      if (validThumbnails.isEmpty) {
        return 'https://via.placeholder.com/120x120';
      }

      // Sort by width to get highest quality
      validThumbnails.sort((a, b) => (b['width'] ?? 0).compareTo(a['width'] ?? 0));

      String url = validThumbnails.first['url'].toString();

      // Enhance URL for better quality
      return url
          .replaceAll('w60-h60', 'w180-h180')
          .replaceAll('w120-h120', 'w180-h180')
          .replaceAll('=w60-h60', '=w180-h180')
          .replaceAll('=w120-h120', '=w180-h180');
    } catch (e) {
      debugPrint('❌ HomeScreen: Error getting enhanced image URL: $e');
      return 'https://via.placeholder.com/120x120';
    }
  }
}

class _AnimatedMusicCard extends StatefulWidget {
  final Widget child;
  final VoidCallback onTap;
  const _AnimatedMusicCard({required this.child, required this.onTap, Key? key}) : super(key: key);
  @override
  State<_AnimatedMusicCard> createState() => _AnimatedMusicCardState();
}

class _AnimatedMusicCardState extends State<_AnimatedMusicCard> {
  double _scale = 1.0;
  double _opacity = 0.0;
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(milliseconds: 50), () {
      if (mounted) setState(() => _opacity = 1.0);
    });
  }
  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      duration: const Duration(milliseconds: 300),
      opacity: _opacity,
      child: GestureDetector(
        onTapDown: (_) => setState(() => _scale = 0.97),
        onTapUp: (_) => setState(() => _scale = 1.0),
        onTapCancel: () => setState(() => _scale = 1.0),
        onTap: widget.onTap,
        child: AnimatedScale(
          scale: _scale,
          duration: const Duration(milliseconds: 120),
          curve: Curves.easeOut,
          child: widget.child,
        ),
      ),
    );
  }
}

class _AnimatedCupertinoButton extends StatefulWidget {
  final Widget child;
  final VoidCallback onPressed;
  final BorderRadius borderRadius;
  final EdgeInsets padding;
  const _AnimatedCupertinoButton({required this.child, required this.onPressed, required this.borderRadius, required this.padding, Key? key}) : super(key: key);
  @override
  State<_AnimatedCupertinoButton> createState() => _AnimatedCupertinoButtonState();
}

class _AnimatedCupertinoButtonState extends State<_AnimatedCupertinoButton> {
  double _scale = 1.0;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => _scale = 0.97),
      onTapUp: (_) => setState(() => _scale = 1.0),
      onTapCancel: () => setState(() => _scale = 1.0),
      onTap: widget.onPressed,
      child: AnimatedScale(
        scale: _scale,
        duration: const Duration(milliseconds: 120),
        curve: Curves.easeOut,
        child: CupertinoButton.filled(
          borderRadius: widget.borderRadius,
          padding: widget.padding,
          onPressed: widget.onPressed,
          child: widget.child,
        ),
      ),
    );
  }
}
