import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:gyawun/generated/l10n.dart';
import 'package:gyawun/ytmusic/ytmusic.dart';
import 'package:gyawun/utils/adaptive_widgets/adaptive_widgets.dart';
import 'package:gyawun/widgets/ios_style_search_bar.dart';
import 'package:gyawun/widgets/ios_style_card.dart';
import 'package:gyawun/widgets/ios_style_transitions.dart';
import 'section_item.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final YTMusic ytMusic = GetIt.I<YTMusic>();
  final ScrollController _scrollController = ScrollController();

  bool initialLoading = true;
  bool nextLoading = false;
  List chips = [];
  List sections = [];
  String? continuation;

  @override
  void initState() {
    super.initState();
    fetchHome();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !nextLoading &&
        continuation != null) {
      fetchNext();
    }
  }

  fetchNext() async {
    if (nextLoading || continuation == null) return;
    setState(() => nextLoading = true);
    Map<String, dynamic> next = await ytMusic.browse(additionalParams: continuation!);
    if (mounted) {
      setState(() {
        nextLoading = false;
        sections.addAll(next['sections'] ?? []);
        continuation = next['continuation'];
      });
    }
  }

  fetchHome() async {
    setState(() {
      initialLoading = true;
      nextLoading = false;
    });
    Map<String, dynamic> home = await ytMusic.browse();
    if (mounted) {
      setState(() {
        initialLoading = false;
        nextLoading = false;
        chips = home['chips'];
        sections = home['sections'];
        continuation = home['continuation'];
      });
    }
  }

  refresh() async {
    if (initialLoading) return;
    Map<String, dynamic> home = await ytMusic.browse();
    if (mounted) {
      setState(() {
        initialLoading = false;
        nextLoading = false;
        chips = home['chips'];
        sections = home['sections'];
        continuation = home['continuation'];
      });
    }
  }

  Widget _horizontalChipsRow(List data) {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: data.length,
        separatorBuilder: (context, index) => const SizedBox(width: 12),
        itemBuilder: (context, index) {
          final element = data[index];
          return IOSStyleChip(
            text: element['title'] ?? '',
            onTap: () => context.go('/chip', extra: element),
          );
        },
      ),
    );
  }

  Widget _buildQuickPicksSection() {
    // Get songs from sections for Quick picks
    List quickPicksSongs = [];
    for (var section in sections) {
      if (section['contents'] != null && section['contents'].isNotEmpty) {
        quickPicksSongs.addAll(section['contents'].take(6));
        break;
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'Quick picks',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        const SizedBox(height: 16),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 3.2,
            ),
            itemCount: quickPicksSongs.length > 6 ? 6 : quickPicksSongs.length,
            itemBuilder: (context, index) {
              final song = quickPicksSongs[index];
              return _buildQuickPickCard(song);
            },
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return AdaptiveScaffold(
      appBar: PreferredSize(
        preferredSize: const AdaptiveAppBar().preferredSize,
        child: AdaptiveAppBar(
          automaticallyImplyLeading: false,
          title: Material(
            color: Colors.transparent,
            child: LayoutBuilder(builder: (context, constraints) {
              return Row(
                children: [
                  ConstrainedBox(
                    constraints: BoxConstraints(
                        maxWidth: constraints.maxWidth > 400
                            ? (400)
                            : constraints.maxWidth),
                    child: IOSStyleSearchBar(
                      hintText: S.of(context).Search_Gyawun,
                      readOnly: true,
                      onTap: () => context.go('/search'),
                    ),
                  ),
                ],
              );
            }),
          ),
          centerTitle: false,
        ),
      ),
      body: initialLoading
          ? const Center(child: AdaptiveProgressRing())
          : Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Theme.of(context).scaffoldBackgroundColor,
                    Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.95),
                  ],
                ),
              ),
              child: RefreshIndicator(
                onRefresh: () => refresh(),
                color: Theme.of(context).colorScheme.primary,
                backgroundColor: Theme.of(context).colorScheme.surface,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.only(top: 8, bottom: 24),
                  controller: _scrollController,
                  physics: const BouncingScrollPhysics(),
                  child: SafeArea(
                    child: Column(
                      children: [
                        _horizontalChipsRow(chips),
                        const SizedBox(height: 8),
                        // Quick picks section - 2 column grid
                        _buildQuickPicksSection(),
                        const SizedBox(height: 24),

                        // Albums for you section - horizontal scroll
                        _buildAlbumsForYouSection(),
                        const SizedBox(height: 24),

                        // Top music videos section
                        _buildTopMusicVideosSection(),
                        const SizedBox(height: 24),

                        // Dynamic sections from API
                        ...sections.asMap().entries.map((entry) {
                          final index = entry.key;
                          final section = entry.value;
                          return IOSStyleAnimatedContainer(
                            duration: Duration(milliseconds: 300 + (index * 100)),
                            child: Padding(
                              padding: const EdgeInsets.only(bottom: 16),
                              child: SectionItem(section: section),
                            ),
                          );
                        }),
                        if (!nextLoading && continuation != null)
                          const SizedBox(height: 50),
                        if (nextLoading)
                          Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.8),
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: const AdaptiveProgressRing(),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildQuickPickCard(Map song) {
    return GestureDetector(
      onTap: () {
        // Handle song tap - you can add play functionality here
        print('Tapped song: ${song['title']}');
      },
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF2A2A2A),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
        children: [
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              bottomLeft: Radius.circular(8),
            ),
            child: Container(
              width: 60,
              height: 60,
              color: Colors.grey[800],
              child: song['thumbnails'] != null && song['thumbnails'].isNotEmpty
                  ? CachedNetworkImage(
                      imageUrl: song['thumbnails'][0]['url'],
                      fit: BoxFit.cover,
                      placeholder: (context, url) => const Icon(Icons.music_note, color: Colors.white54),
                      errorWidget: (context, url, error) =>
                          const Icon(Icons.music_note, color: Colors.white54),
                    )
                  : const Icon(Icons.music_note, color: Colors.white54),
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Text(
                song['title'] ?? 'Unknown',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAlbumsForYouSection() {
    // Get albums from sections
    List albums = [];
    for (var section in sections) {
      if (section['contents'] != null && section['contents'].isNotEmpty) {
        albums.addAll(section['contents'].where((item) =>
          item['type'] == 'ALBUM' || item['type'] == 'PLAYLIST').take(10));
        if (albums.length >= 10) break;
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'Albums for you',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: albums.length,
            itemBuilder: (context, index) {
              final album = albums[index];
              return _buildAlbumCard(album);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAlbumCard(Map album) {
    return Container(
      width: 140,
      margin: const EdgeInsets.only(right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: 140,
              height: 140,
              color: Colors.grey[800],
              child: album['thumbnails'] != null && album['thumbnails'].isNotEmpty
                  ? CachedNetworkImage(
                      imageUrl: album['thumbnails'][0]['url'],
                      fit: BoxFit.cover,
                      placeholder: (context, url) => const Icon(Icons.album, color: Colors.white54, size: 40),
                      errorWidget: (context, url, error) =>
                          const Icon(Icons.album, color: Colors.white54, size: 40),
                    )
                  : const Icon(Icons.album, color: Colors.white54, size: 40),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            album['title'] ?? 'Unknown Album',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          if (album['subtitle'] != null)
            Text(
              album['subtitle'],
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 12,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
        ],
      ),
    );
  }

  Widget _buildTopMusicVideosSection() {
    // Get videos from sections
    List videos = [];
    for (var section in sections) {
      if (section['contents'] != null && section['contents'].isNotEmpty) {
        videos.addAll(section['contents'].where((item) =>
          item['type'] == 'VIDEO' || item['videoId'] != null).take(8));
        if (videos.length >= 8) break;
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'Top music videos',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: videos.length,
            itemBuilder: (context, index) {
              final video = videos[index];
              return _buildVideoCard(video);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildVideoCard(Map video) {
    return Container(
      width: 160,
      margin: const EdgeInsets.only(right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: 160,
              height: 90,
              color: Colors.grey[800],
              child: Stack(
                children: [
                  if (video['thumbnails'] != null && video['thumbnails'].isNotEmpty)
                    CachedNetworkImage(
                      imageUrl: video['thumbnails'][0]['url'],
                      width: 160,
                      height: 90,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => const Icon(Icons.video_library, color: Colors.white54, size: 30),
                      errorWidget: (context, url, error) =>
                          const Icon(Icons.video_library, color: Colors.white54, size: 30),
                    )
                  else
                    const Icon(Icons.video_library, color: Colors.white54, size: 30),
                  const Center(
                    child: Icon(
                      Icons.play_circle_filled,
                      color: Colors.white,
                      size: 40,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            video['title'] ?? 'Unknown Video',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
