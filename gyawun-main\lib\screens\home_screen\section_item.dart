import 'dart:io';
import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:expandable_page_view/expandable_page_view.dart';
import 'package:expandable_text/expandable_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:gyawun/ytmusic/ytmusic.dart';


import '../../services/bottom_message.dart';
import '../../themes/text_styles.dart';
import '../../themes/colors.dart';
import '../../utils/adaptive_widgets/adaptive_widgets.dart';
import '../../utils/enhanced_image.dart';
import '../../utils/extensions.dart';
import '../../services/media_player.dart';
import '../../utils/bottom_modals.dart';
import '../browse_screen/browse_screen.dart';

class SectionItem extends StatefulWidget {
  const SectionItem({required this.section, this.isMore = false, super.key});
  final Map section;
  final bool isMore;

  @override
  State<SectionItem> createState() => _SectionItemState();
}

class _SectionItemState extends State<SectionItem> {
  final ScrollController horizontalScrollController = ScrollController();
  PageController horizontalPageController = PageController();
  bool loadingMore = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    horizontalPageController.dispose();
    horizontalScrollController.dispose();
    super.dispose();
  }

  loadMoreItems() {
    if (widget.section['continuation'] != null) {
      setState(() {
        loadingMore = true;
      });
      GetIt.I<YTMusic>()
          .getMoreItems(continuation: widget.section['continuation'])
          .then((value) {
        setState(() {
          widget.section['contents'].addAll(value['items']);
          widget.section['continuation'] = value['continuation'];
          loadingMore = false;
        });
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    horizontalPageController = PageController(
        viewportFraction: 350 / MediaQuery.of(context).size.width);
    return widget.section['contents'].isEmpty
        ? const SizedBox()
        : Column(
            children: [
              if (widget.section['title'] != null)
                AdaptiveListTile(
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                  title: widget.section['strapline'] == null
                      ? Text(widget.section['title'] ?? '',
                          style: const TextStyle(
                              fontWeight: FontWeight.bold, fontSize: 20))
                      : Text(
                          widget.section['strapline'],
                          style: TextStyle(
                              color: Colors.grey.withAlpha(200), fontSize: 14),
                        ),
                  subtitle: widget.section['strapline'] != null
                      ? Text(widget.section['title'] ?? '',
                          style:
                              mediumTextStyle(context).copyWith(fontSize: 20))
                      : null,
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (widget.section['trailing'] != null)
                        AdaptiveOutlinedButton(
                          onPressed: () async {
                            if (widget.section['trailing']['playable'] ==
                                false) {
                              Navigator.push(
                                context,
                                AdaptivePageRoute.create(
                                  (context) => BrowseScreen(
                                    endpoint: widget.section['trailing']
                                        ['endpoint'],
                                    isMore: true,
                                  ),
                                ),
                              );
                            } else {
                              try {
                                BottomMessage.showText(
                                    context, 'Songs will start playing soon.');

                                var endpoint = widget.section['trailing']['endpoint'];
                                if (endpoint != null) {
                                  Map<String, dynamic> safeEndpoint = {};
                                  if (endpoint is Map<String, dynamic>) {
                                    safeEndpoint = Map<String, dynamic>.from(endpoint);
                                  } else if (endpoint is Map) {
                                    endpoint.forEach((key, value) {
                                      if (key != null) {
                                        safeEndpoint[key.toString()] = value;
                                      }
                                    });
                                  }

                                  await GetIt.I<MediaPlayer>().startPlaylistSongs(safeEndpoint);
                                } else {
                                  debugPrint('❌ SectionItem: Endpoint is null');
                                }
                              } catch (e) {
                                debugPrint('❌ SectionItem: Error in trailing button: $e');
                              }
                            }
                          },
                          child: Text(widget.section['trailing']['text']),
                        ),
                      if (Platform.isWindows &&
                          widget.section['viewType'] != 'SINGLE_COLUMN')
                        AdaptiveIconButton(
                          icon: Icon(AdaptiveIcons.chevron_left),
                          onPressed: () {
                            if (widget.section['viewType'] == 'COLUMN' &&
                                !widget.isMore) {
                              horizontalPageController.previousPage(
                                  duration: const Duration(milliseconds: 200), // Faster animation
                                  curve: Curves.easeOut);
                            } else {
                              horizontalScrollController.animateTo(
                                horizontalScrollController.offset -
                                    horizontalScrollController
                                        .position.extentInside,
                                duration: const Duration(milliseconds: 200), // Faster animation
                                curve: Curves.easeOut,
                              );
                            }
                          },
                        ),
                      if (Platform.isWindows &&
                          widget.section['viewType'] != 'SINGLE_COLUMN')
                        AdaptiveIconButton(
                          icon: Icon(AdaptiveIcons.chevron_right),
                          onPressed: () {
                            if (widget.section['viewType'] == 'COLUMN' &&
                                !widget.isMore) {
                              horizontalPageController.nextPage(
                                  duration: const Duration(milliseconds: 200), // Faster animation
                                  curve: Curves.easeOut);
                            } else {
                              horizontalScrollController.animateTo(
                                horizontalScrollController.offset +
                                    horizontalScrollController
                                        .position.extentInside,
                                duration: const Duration(milliseconds: 200), // Faster animation
                                curve: Curves.easeOut,
                              );
                            }
                          },
                        )
                    ],
                  ),
                  leading: widget.section['thumbnails'] != null &&
                          widget.section['thumbnails']?.isNotEmpty
                      ? CircleAvatar(
                          backgroundImage: CachedNetworkImageProvider(
                            widget.section['thumbnails'].first['url'],
                          ),
                        )
                      : null,
                ),
              if (widget.section['viewType'] == 'COLUMN' && !widget.isMore)
                SongList(
                  songs: widget.section['contents'],
                  controller: horizontalPageController,
                )
              else if (widget.section['viewType'] == 'SINGLE_COLUMN' ||
                  widget.isMore)
                SingleColumnList(songs: widget.section['contents'])
              else
                ItemList(
                  items: widget.section['contents'],
                  controller: horizontalScrollController,
                ),
              if (loadingMore) const AdaptiveProgressRing(),
              if (widget.section['continuation'] != null && !loadingMore)
                AdaptiveButton(
                    onPressed: loadMoreItems, child: const Text("Load More"))
            ],
          );
  }
}

// ignore: must_be_immutable
class SongList extends StatefulWidget {
  SongList({required this.songs, required this.controller, super.key});
  final List songs;
  PageController controller;

  @override
  State<SongList> createState() => _SongListState();
}

class _SongListState extends State<SongList> {
  late int num;
  late int pages;

  @override
  void initState() {
    super.initState();
  }

  @override
  dispose() {
    super.dispose();
    widget.controller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    num = widget.songs.length <= 5 ? widget.songs.length : 4;
    pages = widget.songs.length ~/ num;
    pages = (pages * num) < widget.songs.length ? pages + 1 : pages;
    return ExpandablePageView.builder(
      controller: widget.controller,
      padEnds: false,
      itemCount: pages,
      itemBuilder: (context, index) {
        int start = index * num;
        int end = start + num;
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Column(
            children: widget.songs
                .sublist(start, min(end, widget.songs.length))
                .map((pageSongs) {
              return SongTile(song: pageSongs);
            }).toList(),
          ),
        );
      },
    );
  }
}

class SingleColumnList extends StatelessWidget {
  const SingleColumnList({required this.songs, super.key});
  final List songs;
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: Theme.of(context).brightness == Brightness.dark
              ? [
                  const Color(0xFF0A0A0A).withValues(alpha: 0.8),
                  const Color(0xFF1A1A1A).withValues(alpha: 0.9),
                  const Color(0xFF2A2A2A),
                ]
              : [
                  Colors.white.withValues(alpha: 0.8),
                  const Color(0xFFF8F9FA).withValues(alpha: 0.9),
                  const Color(0xFFF1F3F4),
                ],
          stops: const [0.0, 0.5, 1.0],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.white.withValues(alpha: 0.15)
              : Colors.black.withValues(alpha: 0.08),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.6)
                : Colors.black.withValues(alpha: 0.1),
            blurRadius: 25,
            offset: const Offset(0, 8),
            spreadRadius: 2,
          ),
          if (Theme.of(context).brightness == Brightness.dark) ...[
            BoxShadow(
              color: iOSBlue.withValues(alpha: 0.2),
              blurRadius: 30,
              offset: const Offset(0, 0),
              spreadRadius: -8,
            ),
            BoxShadow(
              color: iOSPurple.withValues(alpha: 0.15),
              blurRadius: 35,
              offset: const Offset(0, 0),
              spreadRadius: -10,
            ),
          ],
        ],
      ),
      child: Column(
        children: songs.asMap().entries.map((entry) {
          int index = entry.key;
          var song = entry.value;
          return Container(
            margin: EdgeInsets.only(
              top: index == 0 ? 8 : 0,
              bottom: index == songs.length - 1 ? 8 : 0,
            ),
            child: SongTile(song: song),
          );
        }).toList(),
      ),
    );
  }
}

class SongTile extends StatelessWidget {
  const SongTile({required this.song, this.playlistId, super.key});
  final String? playlistId;
  final Map song;
  @override
  Widget build(BuildContext context) {
    List thumbnails = song['thumbnails'];
    double height =
        (song['aspectRatio'] != null ? 60 / song['aspectRatio'] : 60) // Increased from 50 to 60
            .toDouble();
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6), // Increased margins
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: Theme.of(context).brightness == Brightness.dark
              ? [
                  const Color(0xFF0A0A0A),
                  const Color(0xFF1A1A1A),
                  const Color(0xFF2A2A2A),
                ]
              : [
                  Colors.white,
                  const Color(0xFFF8F9FA),
                  const Color(0xFFF1F3F4),
                ],
          stops: const [0.0, 0.5, 1.0],
        ),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.white.withValues(alpha: 0.15)
              : Colors.black.withValues(alpha: 0.08),
          width: 1.2,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.6)
                : Colors.black.withValues(alpha: 0.15),
            blurRadius: 20,
            offset: const Offset(0, 6),
            spreadRadius: 1,
          ),
          if (Theme.of(context).brightness == Brightness.dark) ...[
            BoxShadow(
              color: iOSBlue.withValues(alpha: 0.3),
              blurRadius: 25,
              offset: const Offset(0, 0),
              spreadRadius: -5,
            ),
            BoxShadow(
              color: iOSPurple.withValues(alpha: 0.2),
              blurRadius: 30,
              offset: const Offset(0, 0),
              spreadRadius: -8,
            ),
          ] else ...[
            BoxShadow(
              color: iOSBlue.withValues(alpha: 0.1),
              blurRadius: 15,
              offset: const Offset(0, 3),
              spreadRadius: -2,
            ),
          ],
        ],
      ),
      child: AdaptiveListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12), // Increased padding
        onTap: () async {
          try {
            if (song['endpoint'] != null && song['videoId'] == null) {
              Navigator.push(
                context,
                CupertinoPageRoute(
                  builder: (context) =>
                      BrowseScreen(endpoint: song['endpoint'] ?? <String, dynamic>{}),
                ));
            } else {
              // Safely convert song to Map<String, dynamic>
              Map<String, dynamic> safeSong = {};
              if (song is Map<String, dynamic>) {
                safeSong = Map<String, dynamic>.from(song);
              } else if (song is Map) {
                song.forEach((key, value) {
                  if (key != null) {
                    safeSong[key.toString()] = value;
                  }
                });
              }
              await GetIt.I<MediaPlayer>().playSong(safeSong);
            }
          } catch (e) {
            debugPrint('❌ SectionItem: Error playing song: $e');
          }
        },
        onSecondaryTap: () {
          if (song['videoId'] != null) {
            Modals.showSongBottomModal(context, song);
          }
        },
        onLongPress: () {
          if (song['videoId'] != null) {
            Modals.showSongBottomModal(context, song);
          }
        },
        title: Text(
          song['title'] ?? "",
          maxLines: 1,
          style: TextStyle(
            fontSize: 18, // Increased from 16 to 18
            fontWeight: FontWeight.w700,
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.white
                : Colors.black,
            letterSpacing: -0.2,
          ),
        ),
        leading: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.black.withValues(alpha: 0.5)
                    : Colors.black.withValues(alpha: 0.2),
                blurRadius: 12,
                offset: const Offset(0, 3),
              ),
              if (Theme.of(context).brightness == Brightness.dark)
                BoxShadow(
                  color: iOSPurple.withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 0),
                  spreadRadius: -3,
                ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: CachedNetworkImage(
              imageUrl: _getEnhancedImageUrl(thumbnails),
              height: height,
              width: 60, // Increased from 50 to 60
              fit: BoxFit.cover,
              memCacheWidth: 120, // Higher quality memory cache
              memCacheHeight: (height * 2.0).round(),
              maxWidthDiskCache: 180, // Higher quality disk cache
              maxHeightDiskCache: (height * 3.0).round(),
              fadeInDuration: const Duration(milliseconds: 150), // Smooth animations
              fadeOutDuration: const Duration(milliseconds: 75),
              placeholder: (context, url) => Container(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey[800]
                    : Colors.grey[200],
                child: const Icon(Icons.music_note, size: 24),
              ),
              errorWidget: (context, url, error) => Container(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey[800]
                    : Colors.grey[200],
                child: const Icon(Icons.music_note, size: 24),
              ),
            ),
          ),
        ),
        subtitle: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (song['explicit'] == true)
              Padding(
                padding: const EdgeInsets.only(right: 2),
                child: Icon(
                  Icons.explicit,
                  size: 18,
                  color: Colors.grey.withValues(alpha: 0.9),
                ),
              ),
            Expanded(
              child: Text(
                _buildSubtitle(song),
                maxLines: 1,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.8)
                      : Colors.black.withValues(alpha: 0.6),
                  letterSpacing: 0.1,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        trailing: song['endpoint'] != null && song['videoId'] == null
            ? Icon(AdaptiveIcons.chevron_right)
            : null,
        description: (song['type'] == 'EPISODE' && song['description'] != null)
            ? Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  children: [
                    ExpandableText(
                      song['description'].split('\n')?[0] ?? '',
                      expandText: "Show More",
                      collapseText: "Show Less",
                      maxLines: 3,
                      style: TextStyle(color: context.subtitleColor),
                    ),
                  ],
                ),
              )
            : null),
    );
  }

  String _getEnhancedImageUrl(List thumbnails) {
    try {
      // Find the highest quality thumbnail available
      var validThumbnails = thumbnails.where((el) =>
        el != null &&
        el is Map &&
        el['width'] != null &&
        el['url'] != null
      ).toList();

      if (validThumbnails.isEmpty) {
        return 'https://via.placeholder.com/120x120';
      }

      // Sort by width to get highest quality
      validThumbnails.sort((a, b) => (b['width'] ?? 0).compareTo(a['width'] ?? 0));

      String url = validThumbnails.first['url'].toString();

      // Enhance URL for better quality
      return url
          .replaceAll('w60-h60', 'w120-h120')
          .replaceAll('w120-h120', 'w180-h180')
          .replaceAll('=w60-h60', '=w180-h180')
          .replaceAll('=w120-h120', '=w180-h180');
    } catch (e) {
      debugPrint('❌ SongTile: Error getting enhanced image URL: $e');
      return 'https://via.placeholder.com/120x120';
    }
  }

  String _buildSubtitle(Map item) {
    List sub = [];
    if (sub.isEmpty && item['artists'] != null && item['artists'] is List) {
      for (var artist in item['artists']) {
        if (artist != null && artist is Map && artist['name'] != null) {
          sub.add(artist['name'].toString());
        }
      }
    }
    if (sub.isEmpty && item['album'] != null && item['album'] is Map) {
      var albumName = item['album']['name'];
      if (albumName != null) {
        sub.add(albumName.toString());
      }
    }
    String s = sub.join(' · ');
    return item['subtitle']?.toString() ?? s;
  }
}

class ItemList extends StatefulWidget {
  const ItemList({required this.items, this.controller, super.key});

  final List items;
  final ScrollController? controller;

  @override
  State<ItemList> createState() => _ItemListState();
}

class _ItemListState extends State<ItemList> {
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      double height = constraints.maxWidth > 600 ? 200 : 150;

      return SizedBox(
        height: height + (Platform.isWindows ? 120 : 95), // Increased to fix overflow
        child: ListView.separated(
          controller: widget.controller,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
          scrollDirection: Axis.horizontal,
          physics: const BouncingScrollPhysics(), // Smooth iOS-style scrolling
          // Performance optimizations
          cacheExtent: 800, // Increased cache for smoother scrolling
          addAutomaticKeepAlives: false, // Improve memory usage
          addRepaintBoundaries: true, // Optimize repaints
          addSemanticIndexes: false, // Disable for better performance
          itemBuilder: (context, index) {
            double width = height * (widget.items[index]?['aspectRatio']?.toDouble() ?? 1.0);
            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                gradient: Theme.of(context).brightness == Brightness.dark
                    ? LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          const Color(0xFF0A0A0A),
                          const Color(0xFF1A1A1A),
                          const Color(0xFF2A2A2A),
                        ],
                        stops: const [0.0, 0.5, 1.0],
                      )
                    : LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.white,
                          const Color(0xFFF8F9FA),
                          const Color(0xFFF1F3F4),
                        ],
                        stops: const [0.0, 0.5, 1.0],
                      ),
                borderRadius: BorderRadius.circular(iOSCardRadius + 2),
                border: Border.all(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.2)
                      : Colors.black.withValues(alpha: 0.1),
                  width: 1.2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.black.withValues(alpha: 0.6)
                        : Colors.black.withValues(alpha: 0.15),
                    blurRadius: 25,
                    offset: const Offset(0, 8),
                    spreadRadius: 2,
                  ),
                  if (Theme.of(context).brightness == Brightness.dark) ...[
                    BoxShadow(
                      color: iOSBlue.withValues(alpha: 0.3),
                      blurRadius: 30,
                      offset: const Offset(0, 0),
                      spreadRadius: -8,
                    ),
                    BoxShadow(
                      color: iOSPurple.withValues(alpha: 0.2),
                      blurRadius: 40,
                      offset: const Offset(0, 0),
                      spreadRadius: -10,
                    ),
                  ] else ...[
                    BoxShadow(
                      color: iOSBlue.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 4),
                      spreadRadius: -2,
                    ),
                  ],
                ],
              ),
              child: AdaptiveInkWell(
                padding: const EdgeInsets.all(12),
                onTap: () async {
                  try {
                    final item = widget.items[index];
                    if (item == null) {
                      debugPrint('❌ ItemList: Item is null');
                      return;
                    }

                    if (item['endpoint'] != null && item['videoId'] == null) {
                      Navigator.push(
                          context,
                          AdaptivePageRoute.create(
                            (context) => BrowseScreen(endpoint: item['endpoint']),
                          ));
                    } else {
                      // Safely convert item to Map<String, dynamic>
                      Map<String, dynamic> safeItem = {};
                      if (item is Map<String, dynamic>) {
                        safeItem = Map<String, dynamic>.from(item);
                      } else if (item is Map) {
                        item.forEach((key, value) {
                          if (key != null) {
                            safeItem[key.toString()] = value;
                          }
                        });
                      } else {
                        debugPrint('❌ ItemList: Item is not a valid Map type');
                        return;
                      }

                      await GetIt.I<MediaPlayer>().playSong(safeItem);
                    }
                  } catch (e) {
                    debugPrint('❌ ItemList: Error playing song: $e');
                  }
                },
                onSecondaryTap: () {
                  if (widget.items[index]['videoId'] != null) {
                    Modals.showSongBottomModal(context, widget.items[index]);
                  } else if (widget.items[index]['playlistId'] != null) {
                    Modals.showPlaylistBottomModal(
                        context, widget.items[index]);
                  }
                },
                onLongPress: () {
                  if (widget.items[index]['videoId'] != null) {
                    Modals.showSongBottomModal(context, widget.items[index]);
                  } else if (widget.items[index]['playlistId'] != null) {
                    Modals.showPlaylistBottomModal(
                        context, widget.items[index]);
                  }
                },
                borderRadius: BorderRadius.circular(iOSCardRadius),
                child: Column(
                  children: [
                    Container(
                      width: width,
                      height: height,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: Theme.of(context).brightness == Brightness.dark
                              ? [
                                  const Color(0xFF0F0F0F),
                                  const Color(0xFF1F1F1F),
                                  const Color(0xFF2F2F2F),
                                ]
                              : [
                                  const Color(0xFFF5F5F5),
                                  const Color(0xFFE8E8E8),
                                  const Color(0xFFDDDDDD),
                                ],
                          stops: const [0.0, 0.5, 1.0],
                        ),
                        borderRadius: widget.items[index]['type'] == 'ARTIST'
                            ? BorderRadius.circular(height / 2)
                            : BorderRadius.circular(iOSCardRadius + 2),
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.black.withValues(alpha: 0.5)
                                : Colors.black.withValues(alpha: 0.2),
                            blurRadius: 20,
                            offset: const Offset(0, 6),
                            spreadRadius: 2,
                          ),
                          if (Theme.of(context).brightness == Brightness.dark) ...[
                            BoxShadow(
                              color: iOSPurple.withValues(alpha: 0.3),
                              blurRadius: 25,
                              offset: const Offset(0, 0),
                              spreadRadius: -5,
                            ),
                            BoxShadow(
                              color: iOSBlue.withValues(alpha: 0.2),
                              blurRadius: 30,
                              offset: const Offset(0, 0),
                              spreadRadius: -8,
                            ),
                          ] else ...[
                            BoxShadow(
                              color: iOSBlue.withValues(alpha: 0.15),
                              blurRadius: 15,
                              offset: const Offset(0, 3),
                              spreadRadius: -2,
                            ),
                          ],
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: widget.items[index]['type'] == 'ARTIST'
                            ? BorderRadius.circular(height / 2)
                            : BorderRadius.circular(iOSCardRadius),
                        child: CachedNetworkImage(
                          imageUrl: getEnhancedImage(
                              widget.items[index]['thumbnails'].first['url'],
                              dp: 2.0, // Increased for better quality
                              width: width),
                          fit: BoxFit.cover,
                          memCacheWidth: (width * 2.5).round(), // Higher quality cache
                          memCacheHeight: (height * 2.5).round(),
                          maxWidthDiskCache: (width * 3.0).round(), // Better disk cache
                          maxHeightDiskCache: (height * 3.0).round(),
                          fadeInDuration: const Duration(milliseconds: 150), // Smooth fade
                          fadeOutDuration: const Duration(milliseconds: 75),
                          placeholder: (context, url) => Container(
                            color: Colors.grey.withValues(alpha: 0.2),
                            child: Icon(
                              CupertinoIcons.music_note,
                              color: Colors.grey.withValues(alpha: 0.6),
                              size: height * 0.3,
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            color: Colors.grey.withValues(alpha: 0.2),
                            child: Icon(
                              CupertinoIcons.music_note,
                              color: Colors.grey.withValues(alpha: 0.6),
                              size: height * 0.3,
                            ),
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 16), // Add bottom padding to prevent overflow
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Flexible(
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    widget.items[index]['title']
                                        .toString()
                                        .breakWord,
                                    maxLines: 2,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      height: 1.2,
                                      fontSize: 15,
                                      fontWeight: FontWeight.w700,
                                      color: Theme.of(context).brightness == Brightness.dark
                                          ? Colors.white
                                          : Colors.black,
                                      letterSpacing: -0.2,
                                      shadows: Theme.of(context).brightness == Brightness.dark
                                          ? [
                                              Shadow(
                                                color: Colors.black.withValues(alpha: 0.5),
                                                offset: const Offset(0, 1),
                                                blurRadius: 2,
                                              ),
                                            ]
                                          : null,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 4),
                                  if (_buildSubtitle(widget.items[index]) != null)
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        if (widget.items[index]['explicit'] == true)
                                          Padding(
                                            padding: const EdgeInsets.only(right: 4),
                                            child: Icon(
                                              Icons.explicit,
                                              size: 12,
                                              color: Colors.grey.withValues(alpha: 0.9),
                                            ),
                                          ),
                                        Flexible(
                                          child: Text(
                                            _buildSubtitle(widget.items[index])!,
                                            maxLines: 1,
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                                color: Theme.of(context).brightness == Brightness.dark
                                                    ? Colors.white.withValues(alpha: 0.8)
                                                    : Colors.black.withValues(alpha: 0.6),
                                                fontSize: 13,
                                                fontWeight: FontWeight.w500,
                                                height: 1.2,
                                                letterSpacing: 0.1),
                                            overflow: TextOverflow.ellipsis),
                                        ),
                                      ],
                                    ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            );
          },
          separatorBuilder: (context, index) => const SizedBox(width: 8),
          itemCount: widget.items.length,
        ),
      );
    });
  }

  String? _buildSubtitle(Map item) {
    List sub = [];
    if (item['subtitle'] != null) {
      try {
        sub.addAll(item['subtitle'].toString().split('•'));
      } catch (e) {
        sub.add(item['subtitle'].toString());
      }
    } else if (item['description'] != null) {
      try {
        sub.addAll(item['description'].toString().split(','));
      } catch (e) {
        sub.add(item['description'].toString());
      }
    } else if (item['year'] != null) {
      sub.add(item['year'].toString());
    }
    String s = sub.join(' • ').trim();
    return s.isEmpty ? null : s;
  }
}
