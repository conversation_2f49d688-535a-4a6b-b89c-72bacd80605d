import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent_ui;
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../services/media_player.dart';
import '../../utils/adaptive_widgets/adaptive_widgets.dart';
import '../../utils/enhanced_image.dart';

class BottomPlayer extends StatelessWidget {
  const BottomPlayer({super.key});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
        valueListenable: GetIt.I<MediaPlayer>().currentSongNotifier,
        builder: (context, currentSong, child) {
          return currentSong != null
              ? SafeArea(
                  bottom: false,
                  child: Container(
                    color: Platform.isWindows
                        ? fluent_ui.FluentTheme.of(context)
                            .scaffoldBackgroundColor
                        : Colors.transparent,
                    padding: const EdgeInsets.only(bottom: 8),
                    child: GestureDetector(
                      onTap: () {
                        context.push('/player');
                      },
                      child: Dismissible(
                          key: Key('bottomplayer${currentSong.id}'),
                          direction: DismissDirection.down,
                          confirmDismiss: (direction) async {
                            await GetIt.I<MediaPlayer>().stop();
                            return true;
                          },
                          child: Container(
                            height: 72,
                            margin: const EdgeInsets.symmetric(horizontal: 12),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: Theme.of(context).brightness == Brightness.dark
                                    ? [
                                        const Color(0xFF1C1C1E),
                                        const Color(0xFF2C2C2E),
                                        const Color(0xFF3A3A3C),
                                      ]
                                    : [
                                        Colors.white,
                                        const Color(0xFFF2F2F7),
                                        const Color(0xFFE5E5EA),
                                      ],
                                stops: const [0.0, 0.5, 1.0],
                              ),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: Theme.of(context).brightness == Brightness.dark
                                    ? Colors.white.withValues(alpha: 0.12)
                                    : Colors.black.withValues(alpha: 0.06),
                                width: 0.5,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Colors.black.withValues(alpha: 0.6)
                                      : Colors.black.withValues(alpha: 0.08),
                                  blurRadius: 20,
                                  offset: const Offset(0, 4),
                                  spreadRadius: 0,
                                ),
                                BoxShadow(
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Colors.black.withValues(alpha: 0.3)
                                      : Colors.black.withValues(alpha: 0.04),
                                  blurRadius: 6,
                                  offset: const Offset(0, 1),
                                  spreadRadius: 0,
                                ),
                              ],
                            ),
                            child: Dismissible(
                              key: Key(currentSong.id),
                              confirmDismiss: (direction) async {
                                if (direction == DismissDirection.startToEnd) {
                                  await GetIt.I<MediaPlayer>()
                                      .player
                                      .seekToPrevious();
                                } else {
                                  await GetIt.I<MediaPlayer>().player.seekToNext();
                                }
                                return Future.value(false);
                              },
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 12),
                                child: Row(
                                  children: [
                                    // Album Art
                                    Container(
                                      width: 48,
                                      height: 48,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(12),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withValues(alpha: 0.2),
                                            blurRadius: 8,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(12),
                                        child: currentSong.extras?['offline'] == true &&
                                                !currentSong.artUri
                                                    .toString()
                                                    .startsWith('https')
                                            ? Image.file(
                                                File.fromUri(currentSong.artUri!),
                                                width: 48,
                                                height: 48,
                                                fit: BoxFit.cover,
                                              )
                                            : CachedNetworkImage(
                                                imageUrl: getEnhancedImage(
                                                    currentSong.extras!['thumbnails']
                                                        .first['url'],
                                                    dp: MediaQuery.of(context)
                                                        .devicePixelRatio,
                                                    width: 48),
                                                width: 48,
                                                height: 48,
                                                fit: BoxFit.cover,
                                                errorWidget: (context, url, error) {
                                                  return Container(
                                                    width: 48,
                                                    height: 48,
                                                    color: Colors.grey.withValues(alpha: 0.3),
                                                    child: const Icon(Icons.music_note),
                                                  );
                                                },
                                              ),
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    // Song Info
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            currentSong.title,
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600,
                                              color: Theme.of(context).brightness == Brightness.dark
                                                  ? Colors.white
                                                  : Colors.black,
                                            ),
                                          ),
                                          if (currentSong.artist != null ||
                                              currentSong.extras!['subtitle'] != null)
                                            Text(
                                              currentSong.artist ??
                                                  currentSong.extras!['subtitle'],
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                              style: TextStyle(
                                                fontSize: 14,
                                                fontWeight: FontWeight.w400,
                                                color: Theme.of(context).brightness == Brightness.dark
                                                    ? Colors.white.withValues(alpha: 0.7)
                                                    : Colors.black.withValues(alpha: 0.6),
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                    // Controls
                                    Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        ValueListenableBuilder(
                                          valueListenable:
                                              GetIt.I<MediaPlayer>().buttonState,
                                          builder: (context, buttonState, child) {
                                            return (buttonState == ButtonState.loading)
                                                ? SizedBox(
                                                    width: 32,
                                                    height: 32,
                                                    child: CircularProgressIndicator(
                                                      strokeWidth: 2,
                                                      valueColor: AlwaysStoppedAnimation<Color>(
                                                        Theme.of(context).brightness == Brightness.dark
                                                            ? Colors.white
                                                            : Colors.black,
                                                      ),
                                                    ),
                                                  )
                                                : IconButton(
                                                    onPressed: () {
                                                      GetIt.I<MediaPlayer>()
                                                              .player
                                                              .playing
                                                          ? GetIt.I<MediaPlayer>()
                                                              .player
                                                              .pause()
                                                          : GetIt.I<MediaPlayer>()
                                                              .player
                                                              .play();
                                                    },
                                                    icon: Icon(
                                                      buttonState == ButtonState.playing
                                                          ? Icons.pause_rounded
                                                          : Icons.play_arrow_rounded,
                                                      size: 32,
                                                      color: Theme.of(context).brightness == Brightness.dark
                                                          ? Colors.white
                                                          : Colors.black,
                                                    ),
                                                  );
                                          },
                                        ),
                                        if (context.watch<MediaPlayer>().player.hasNext)
                                          IconButton(
                                            onPressed: () {
                                              GetIt.I<MediaPlayer>()
                                                  .player
                                                  .seekToNext();
                                            },
                                            icon: Icon(
                                              Icons.skip_next_rounded,
                                              size: 28,
                                              color: Theme.of(context).brightness == Brightness.dark
                                                  ? Colors.white
                                                  : Colors.black,
                                            ),
                                          ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                          ),
                        ),
                      ),
                    ),
                  ),
                )
              : const SizedBox.shrink();
        });
  }
}
