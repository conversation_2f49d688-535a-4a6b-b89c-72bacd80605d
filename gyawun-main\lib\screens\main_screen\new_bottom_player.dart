import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';

import '../../services/media_player.dart';
import '../../utils/enhanced_image.dart';

class NewBottomPlayer extends StatelessWidget {
  const NewBottomPlayer({super.key});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: GetIt.I<MediaPlayer>().currentSongNotifier,
      builder: (context, currentSong, child) {
        if (currentSong == null) return const SizedBox.shrink();
        
        return Container(
          margin: const EdgeInsets.fromLTRB(12, 8, 12, 20), // Extra margin to prevent overlap
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: Theme.of(context).brightness == Brightness.dark
                  ? [
                      const Color(0xFF2A2A2A), // Dark gray
                      const Color(0xFF3A3A3A), // Medium gray
                      const Color(0xFF1A1A1A), // Darker gray
                    ]
                  : [
                      const Color(0xFFFFFFFF), // White
                      const Color(0xFFF8F9FA), // Light gray
                      const Color(0xFFE9ECEF), // Lighter gray
                    ],
              stops: const [0.0, 0.5, 1.0],
            ),
            borderRadius: BorderRadius.circular(20), // More rounded
            border: Border.all(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white.withValues(alpha: 0.1)
                  : Colors.black.withValues(alpha: 0.05),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.black.withValues(alpha: 0.6)
                    : Colors.black.withValues(alpha: 0.15),
                blurRadius: 20,
                offset: const Offset(0, 8),
                spreadRadius: 2,
              ),
              BoxShadow(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.black.withValues(alpha: 0.3)
                    : Colors.black.withValues(alpha: 0.08),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(16),
              onTap: () => context.push('/player'),
              child: Container(
                height: 64,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  children: [
                    // Album Art
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: _buildAlbumArt(currentSong, context),
                      ),
                    ),
                    const SizedBox(width: 12),
                    
                    // Song Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            currentSong.title,
                            style: TextStyle(
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.white
                                  : Colors.black,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (currentSong.artist != null)
                            Text(
                              currentSong.artist!,
                              style: TextStyle(
                                color: Theme.of(context).brightness == Brightness.dark
                                    ? Colors.white.withValues(alpha: 0.7)
                                    : Colors.black.withValues(alpha: 0.6),
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                        ],
                      ),
                    ),
                    
                    // Play/Pause Button
                    Container(
                      width: 44,
                      height: 44,
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Color(0xFF1DB954),
                            Color(0xFF1ED760),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(22),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF1DB954).withValues(alpha: 0.4),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: ValueListenableBuilder(
                        valueListenable: GetIt.I<MediaPlayer>().buttonState,
                        builder: (context, buttonState, child) {
                          return IconButton(
                            onPressed: () {
                              if (buttonState == ButtonState.playing) {
                                GetIt.I<MediaPlayer>().player.pause();
                              } else if (buttonState == ButtonState.paused) {
                                GetIt.I<MediaPlayer>().player.play();
                              }
                            },
                            icon: Icon(
                              buttonState == ButtonState.playing
                                  ? Icons.pause_rounded
                                  : buttonState == ButtonState.loading
                                      ? Icons.hourglass_empty_rounded
                                      : Icons.play_arrow_rounded,
                              color: Colors.white,
                              size: 24,
                            ),
                            padding: EdgeInsets.zero,
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAlbumArt(currentSong, BuildContext context) {
    if (currentSong.extras?['offline'] == true &&
        !currentSong.artUri.toString().startsWith('https')) {
      return Image.file(
        File.fromUri(currentSong.artUri!),
        fit: BoxFit.cover,
      );
    }
    
    if (currentSong.extras?['thumbnails'] != null &&
        currentSong.extras!['thumbnails'].isNotEmpty) {
      return CachedNetworkImage(
        imageUrl: getEnhancedImage(
          currentSong.extras!['thumbnails'].first['url'],
          dp: MediaQuery.of(context).devicePixelRatio,
          width: 48,
        ),
        fit: BoxFit.cover,
        errorWidget: (context, url, error) => Container(
          color: Colors.black.withValues(alpha: 0.1),
          child: const Icon(
            Icons.music_note,
            color: Colors.white,
            size: 24,
          ),
        ),
      );
    }
    
    return Container(
      color: Colors.black.withValues(alpha: 0.1),
      child: const Icon(
        Icons.music_note,
        color: Colors.white,
        size: 24,
      ),
    );
  }
}
