import 'dart:io';
import 'dart:math';
import 'dart:ui';
import 'package:audio_video_progress_bar/audio_video_progress_bar.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:gyawun/screens/main_screen/main_screen.dart';
import 'package:gyawun/utils/extensions.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:just_audio/just_audio.dart';
import 'package:just_audio_background/just_audio_background.dart';
import 'package:provider/provider.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';
import 'package:text_scroll/text_scroll.dart';
import 'package:window_manager/window_manager.dart';


import '../../services/download_manager.dart';
import '../../services/media_player.dart';
import '../../themes/colors.dart';
import '../../themes/dark.dart';
import '../../themes/text_styles.dart';
import '../../utils/adaptive_widgets/adaptive_widgets.dart';
import '../../utils/bottom_modals.dart';
import '../../utils/enhanced_image.dart';
import '../../ytmusic/ytmusic.dart';
import 'lyrics_box.dart';
import 'play_button.dart';
import 'queue_list.dart';

class PlayerScreen extends StatefulWidget {
  const PlayerScreen({super.key, this.videoId});
  final String? videoId;

  @override
  State<PlayerScreen> createState() => _PlayerScreenState();
}

class _PlayerScreenState extends State<PlayerScreen> {
  late PanelController panelController;
  final GlobalKey<ScaffoldState> _key = GlobalKey();
  List? colors = [];
  String? image;
  bool canPop = false;
  bool showLyrics = false;
  bool fetchedSong = false;
  late MediaItem? currentSong;

  @override
  void initState() {
    super.initState();
    panelController = PanelController();
    if (widget.videoId != null) {
      GetIt.I<YTMusic>().getSongDetails(widget.videoId!).then((song) {
        if (song != null) {
          GetIt.I<MediaPlayer>().playSong(song);
          setState(() {
            fetchedSong = true;
          });
        }
      });
    }
    currentSong = GetIt.I<MediaPlayer>().currentSongNotifier.value;
    _fetchImage();
    GetIt.I<MediaPlayer>().currentSongNotifier.addListener(songListener);
  }

  @override
  dispose() {
    GetIt.I<MediaPlayer>().currentSongNotifier.removeListener(songListener);
    super.dispose();
  }

  songListener() {
    if (currentSong != GetIt.I<MediaPlayer>().currentSongNotifier.value) {
      if (mounted) {
        setState(() {
          currentSong = GetIt.I<MediaPlayer>().currentSongNotifier.value;
        });
      }
      _fetchImage();
    }
  }

  setShowLyrics() {
    if (mounted) {
      setState(() {
        showLyrics = !showLyrics;
      });
    }
  }

  _fetchImage() {
    if (!mounted) return;
    if (currentSong?.extras?['thumbnails'] != null &&
        currentSong?.extras?['thumbnails'].isNotEmpty &&
        image !=
            getEnhancedImage(
                currentSong?.extras?['thumbnails']?.first['url'])) {
      if (mounted) {
        setState(() {
          image = getEnhancedImage(
            currentSong?.extras?['thumbnails']?.first['url'],
          );
        });
      }
    }
  }

  Future<Color?> getColor(String? image, bool isDark) async {
    if (image == null) return Theme.of(context).scaffoldBackgroundColor;
    final c = await ColorScheme.fromImageProvider(
      provider: CachedNetworkImageProvider(
        image,
        errorListener: (p0) {
          if (mounted) {
            setState(() {
              image = getEnhancedImage(image!,
                  dp: MediaQuery.of(context).devicePixelRatio,
                  quality: 'medium');
            });
          }
        },
      ),
    );
    return c.primary;
    // PaletteGenerator paletteGenerator =
    //     await PaletteGenerator.fromImageProvider(
    //   CachedNetworkImageProvider(
    //     image,
    //     errorListener: (p0) {
    //       if (mounted) {
    //         setState(() {
    //           image = getEnhancedImage(image!,
    //               dp: MediaQuery.of(context).devicePixelRatio,
    //               quality: 'medium');
    //         });
    //       }
    //     },
    //   ),
    // );

    // if (mounted) {
    //   if (isDark) {
    //     return paletteGenerator.darkVibrantColor?.color ??
    //         paletteGenerator.dominantColor?.color ??
    //         paletteGenerator.darkMutedColor?.color ??
    //         paletteGenerator.lightVibrantColor?.color ??
    //         paletteGenerator.lightMutedColor?.color;
    //   } else {
    //     return paletteGenerator.lightMutedColor?.color ??
    //         paletteGenerator.darkVibrantColor?.color ??
    //         paletteGenerator.dominantColor?.color ??
    //         paletteGenerator.darkMutedColor?.color ??
    //         paletteGenerator.lightVibrantColor?.color;
    //   }
    // } else {
    //   return Colors.transparent;
    // }
  }

  MaterialColor primaryWhite = const MaterialColor(
    0xFFFFFFFF,
    <int, Color>{
      50: Color(0xFFFFFFFF),
      100: Color(0xFFFFFFFF),
      200: Color(0xFFFFFFFF),
      300: Color(0xFFFFFFFF),
      400: Color(0xFFFFFFFF),
      500: Color(0xFFFFFFFF),
      600: Color(0xFFFFFFFF),
      700: Color(0xFFFFFFFF),
      800: Color(0xFFFFFFFF),
      900: Color(0xFFFFFFFF),
    },
  );

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: darkTheme(
        colorScheme: ColorScheme.fromSeed(
          seedColor: primaryWhite,
          primary: primaryWhite,
          brightness: Brightness.dark,
        ),
      ),
      child: (widget.videoId != null && fetchedSong == false)
          ? const Center(
              child: AdaptiveProgressRing(),
            )
          // ignore: deprecated_member_use
          : WillPopScope(
              onWillPop: () async {
                if (panelController.isAttached && panelController.isPanelOpen) {
                  await panelController.close();
                  return false;
                }
                return true;
              },
              child: AnnotatedRegion<SystemUiOverlayStyle>(
                value: const SystemUiOverlayStyle(
                  statusBarBrightness: Brightness.dark,
                  statusBarColor: Colors.transparent,
                  statusBarIconBrightness: Brightness.light,
                  systemNavigationBarColor: Colors.transparent,
                ),
                child: FutureBuilder<Color?>(
                    future: getColor(image, context.isDarkMode),
                    builder: (context, snapshot) {
                      if (!mounted) return Container();
                      // pprint(snapshot.data?.toString());
                      return Container(
                        color: Colors.black,
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeIn,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                snapshot.hasData && snapshot.data != null
                                    ? snapshot.data!.withAlpha(200)
                                    : Colors.transparent,
                                snapshot.hasData && snapshot.data != null
                                    ? snapshot.data!.withAlpha(80)
                                    : Colors.transparent
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                          ),
                          child: Scaffold(
                            appBar: PreferredSize(
                              preferredSize: AppBar().preferredSize,
                              child: DragToMoveArea(
                                child: AppBar(
                                  backgroundColor: Colors.transparent,
                                  surfaceTintColor: Colors.transparent,
                                  elevation: 0,
                                  iconTheme:
                                      const IconThemeData(color: Colors.white),
                                  leading: AdaptiveIconButton(
                                    onPressed: () {
                                      context.pop();
                                    },
                                    icon: Icon(AdaptiveIcons.chevron_down),
                                  ),
                                  actions: [
                                    Tooltip(
                                      message: "Lyrics",
                                      child: AdaptiveIconButton(
                                        onPressed: () {
                                          setState(() {
                                            showLyrics = !showLyrics;
                                          });
                                        },
                                        icon: Icon(AdaptiveIcons.lyrics),
                                      ),
                                    ),
                                    if (MediaQuery.of(context).size.width >
                                            MediaQuery.of(context)
                                                .size
                                                .height ||
                                        Platform.isWindows)
                                      AdaptiveIconButton(
                                        onPressed: () {
                                          _key.currentState?.openEndDrawer();
                                        },
                                        icon: Icon(AdaptiveIcons.queue),
                                      ),
                                    if (Platform.isWindows)
                                      const WindowButtons()
                                  ],
                                ),
                              ),
                            ),
                            key: _key,
                            backgroundColor: Colors.transparent,
                            endDrawer: MediaQuery.of(context).size.width >
                                        MediaQuery.of(context).size.height ||
                                    Platform.isWindows
                                ? SizedBox(
                                    width: min(400,
                                            MediaQuery.of(context).size.width) -
                                        50,
                                    child: const QueueList(),
                                  )
                                : null,
                            body: SizedBox(
                              width: double.maxFinite,
                              child: LayoutBuilder(
                                builder: (context, constraints) {
                                  EdgeInsets padding =
                                      MediaQuery.of(context).padding;
                                  double maxWidth = constraints.maxWidth -
                                      padding.left -
                                      padding.right;
                                  double maxHeight = constraints.maxHeight -
                                      padding.top -
                                      padding.bottom;
                                  if (maxWidth > maxHeight) {
                                    return Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceEvenly,
                                      children: [
                                        Artwork(
                                          setShowLyrics: setShowLyrics,
                                          showLyrics: showLyrics,
                                          width: maxWidth / 2.3,
                                          song: currentSong,
                                        ),
                                        NameAndControls(
                                          song: currentSong,
                                          width: maxWidth - (maxWidth / 2.3),
                                          height: maxHeight,
                                          isRow: true,
                                        )
                                      ],
                                    );
                                  }
                                  return Stack(
                                    children: [
                                      Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceAround,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Artwork(
                                            setShowLyrics: setShowLyrics,
                                            showLyrics: showLyrics,
                                            width:
                                                min(maxWidth, maxHeight / 2.2) -
                                                    24,
                                            song: currentSong,
                                          ),
                                          NameAndControls(
                                            song: currentSong,
                                            width: maxWidth,
                                            height: maxHeight -
                                                min(maxWidth, maxHeight / 2.2) -
                                                24,
                                          )
                                        ],
                                      ),
                                      SlidingUpPanel(
                                        controller: panelController,
                                        color: Colors.transparent,
                                        padding: EdgeInsets.zero,
                                        margin: EdgeInsets.zero,
                                        borderRadius: const BorderRadius.only(
                                          topLeft: Radius.circular(20),
                                          topRight: Radius.circular(20),
                                        ),
                                        boxShadow: const [],
                                        minHeight: 50 +
                                            MediaQuery.of(context)
                                                .padding
                                                .bottom,
                                        panel: ClipRRect(
                                          borderRadius: const BorderRadius.only(
                                              topLeft: Radius.circular(20),
                                              topRight: Radius.circular(20)),
                                          child: Container(
                                            width: constraints.maxWidth,
                                            alignment: Alignment.center,
                                            decoration: const BoxDecoration(
                                              borderRadius: BorderRadius.only(
                                                  topLeft: Radius.circular(20),
                                                  topRight:
                                                      Radius.circular(20)),
                                            ),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              mainAxisSize: MainAxisSize.max,
                                              children: [
                                                ClipRRect(
                                                  child: BackdropFilter(
                                                    filter: ImageFilter.blur(
                                                        sigmaX: 3, sigmaY: 3),
                                                    child: Container(
                                                      height: 50 +
                                                          MediaQuery.of(context)
                                                              .padding
                                                              .bottom,
                                                      width: double.maxFinite,
                                                      decoration: BoxDecoration(
                                                        color: Theme.of(context)
                                                            .scaffoldBackgroundColor
                                                            .withAlpha(70),
                                                        borderRadius:
                                                            const BorderRadius
                                                                .only(
                                                                topLeft: Radius
                                                                    .circular(
                                                                        20),
                                                                topRight: Radius
                                                                    .circular(
                                                                        20)),
                                                      ),
                                                      child: Column(
                                                        mainAxisSize:
                                                            MainAxisSize.max,
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .center,
                                                        children: [
                                                          Container(
                                                            height: 5,
                                                            width: 50,
                                                            decoration:
                                                                BoxDecoration(
                                                              color: greyColor,
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          20),
                                                            ),
                                                          ),
                                                          const SizedBox(
                                                              height: 8),
                                                          Text(
                                                            "Next Up",
                                                            style: textStyle(
                                                                context,
                                                                bold: true),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                const Expanded(
                                                    child: QueueList())
                                              ],
                                            ),
                                          ),
                                        ),
                                      )
                                    ],
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      );
                    }),
              ),
            ),
    );
  }
}

class Artwork extends StatelessWidget {
  const Artwork(
      {this.song,
      required this.width,
      required this.showLyrics,
      required this.setShowLyrics,
      super.key});
  final double width;
  final MediaItem? song;
  final bool showLyrics;
  final Function setShowLyrics;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: width,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: song == null
            ? Icon(
                Icons.music_note,
                size: width * 0.5,
              )
            : SafeArea(
                child: LayoutBuilder(builder: (context, constraints) {
                  return GestureDetector(
                    onTap: () {
                      setShowLyrics();
                    },
                    child: Center(
                      child: showLyrics
                          ? LyricsBox(
                              currentSong: song!, size: Size(width, width))
                          : Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(24),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.4),
                                    spreadRadius: 12,
                                    blurRadius: 24,
                                    offset: const Offset(0, 8),
                                  ),
                                ],
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(iOSBorderRadius),
                                child: song?.extras?['offline'] == true &&
                                        !song!.artUri
                                            .toString()
                                            .startsWith('https')
                                    ? Image.file(
                                        File.fromUri(song!.artUri!),
                                      )
                                    : CachedNetworkImage(
                                        filterQuality: FilterQuality.high,
                                        imageUrl: getEnhancedImage(
                                          song!.extras!['thumbnails']
                                              .first['url'],
                                        ),
                                        errorWidget: (context, url, error) {
                                          return CachedNetworkImage(
                                            imageUrl: getEnhancedImage(
                                              song!.extras!['thumbnails']
                                                  .first['url'],
                                              quality: 'medium',
                                            ),
                                          );
                                        },
                                      ),
                              ),
                            ),
                    ),
                  );
                }),
              ),
      ),
    );
  }
}

class NameAndControls extends StatelessWidget {
  const NameAndControls(
      {this.song,
      required this.height,
      required this.width,
      this.isRow = false,
      super.key});
  final double width;
  final double height;
  final MediaItem? song;
  final bool isRow;

  @override
  Widget build(BuildContext context) {
    MediaPlayer mediaPlayer = context.watch<MediaPlayer>();
    return SizedBox(
      height: height,
      width: width,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                TextScroll(
                  song?.title ?? 'Title',
                  style: bigTextStyle(context, bold: true),
                  mode: TextScrollMode.endless,
                ),
                Text(
                  song?.artist ??
                      song?.album ??
                      song?.extras?['subtitle'] ??
                      '',
                  style: smallTextStyle(context),
                )
              ],
            ),
            Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ValueListenableBuilder(
                  valueListenable: mediaPlayer.progressBarState,
                  builder: (context, ProgressBarState value, child) {
                    return ProgressBar(
                      progress: value.current,
                      total: value.total,
                      buffered: value.buffered,
                      barHeight: 3,
                      thumbRadius: 5,
                      onSeek: (value) => mediaPlayer.player.seek(value),
                    );
                  },
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ValueListenableBuilder(
                      valueListenable: Hive.box('FAVOURITES').listenable(),
                      builder: (context, value, child) {
                        Map? item = value.get(song?.extras?['videoId']);
                        return AdaptiveIconButton(
                          icon: Icon(
                            item == null
                                ? AdaptiveIcons.heart
                                : AdaptiveIcons.heart_fill,
                            size: 30,
                          ),
                          onPressed: () async {
                            if (item == null) {
                              await Hive.box('FAVOURITES').put(
                                song!.extras!['videoId'],
                                {
                                  ...song!.extras!,
                                  'createdAt':
                                      DateTime.now().millisecondsSinceEpoch
                                },
                              );
                            } else {
                              await value.delete(song!.extras!['videoId']);
                            }
                          },
                        );
                      },
                    ),
                    AdaptiveIconButton(
                      onPressed: () {
                        mediaPlayer.player.seekToPrevious();
                      },
                      icon: Icon(
                        AdaptiveIcons.skip_previous,
                        size: 30,
                      ),
                    ),
                    const PlayButton(size: 40),
                    AdaptiveIconButton(
                      onPressed: () {
                        mediaPlayer.player.seekToNext();
                      },
                      icon: Icon(
                        AdaptiveIcons.skip_next,
                        size: 30,
                      ),
                    ),
                    ValueListenableBuilder(
                        valueListenable: mediaPlayer.loopMode,
                        builder: (context, value, child) {
                          return AdaptiveIconButton(
                            onPressed: () {
                              mediaPlayer.changeLoopMode();
                            },
                            isSelected: value != LoopMode.off,
                            icon: Icon(
                              value == LoopMode.off || value == LoopMode.all
                                  ? AdaptiveIcons.repeat_all
                                  : AdaptiveIcons.repeat_one,
                              size: 30,
                              color: value == LoopMode.off
                                  ? Colors.white.withValues(alpha: 0.3)
                                  : null,
                            ),
                          );
                        }),
                  ],
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (song != null)
                  RepaintBoundary(
                    child: ValueListenableBuilder(
                      valueListenable: GetIt.I<DownloadManager>().downloads,
                      builder: (context, value, child) {
                        List<Map> elements = value
                            .where((item) => item['videoId'] == song!.id)
                            .toList();
                        Map? item = elements.isNotEmpty ? elements.first : null;
                        if (item != null) {
                          if (item['status'] == 'PROCESSING' ||
                              item['status'] == 'DOWNLOADING') {
                            return AdaptiveProgressRing(
                              value: item['status'] == 'DOWNLOADING'
                                  ? item['progress'] / 100
                                  : null,
                              color: Colors.white,
                              backgroundColor: Colors.black,
                            );
                          } else if (item['status'] == 'DOWNLOADED') {
                            return const Icon(Icons.download_done_outlined);
                          }
                        }
                        return AdaptiveIconButton(
                            onPressed: () {
                              GetIt.I<DownloadManager>()
                                  .downloadSong(song!.extras!);
                            },
                            icon: Icon(
                              AdaptiveIcons.download,
                              size: 30,
                            ));
                      },
                    ),
                  ),
                AdaptiveIconButton(
                    onPressed: () {
                      Modals.showPlayerOptionsModal(
                        context,
                        mediaPlayer.currentSongNotifier.value!.extras!,
                      );
                    },
                    icon: Icon(
                      AdaptiveIcons.more_vertical,
                      size: 30,
                    ))
              ],
            ),
            if (song != null && !isRow)
              SizedBox(height: 55 + MediaQuery.of(context).padding.bottom)
          ],
        ),
      ),
    );
  }
}
