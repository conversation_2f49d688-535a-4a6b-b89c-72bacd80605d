import 'package:flutter/material.dart';
import 'package:flutter_swipe_action_cell/core/cell.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'dart:ui';

import '../../utils/adaptive_widgets/adaptive_widgets.dart';
import '../../utils/bottom_modals.dart';
import '../home_screen/section_item.dart';

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> with SingleTickerProviderStateMixin {
  late AnimationController _sectionController;
  late Animation<double> _sectionFade;
  late Animation<Offset> _sectionSlide;

  @override
  void initState() {
    super.initState();
    _sectionController = AnimationController(
      duration: const Duration(milliseconds: 900),
      vsync: this,
    );
    _sectionFade = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(
      parent: _sectionController,
      curve: Curves.easeIn,
    ));
    _sectionSlide = Tween<Offset>(begin: const Offset(0, 0.08), end: Offset.zero).animate(CurvedAnimation(
      parent: _sectionController,
      curve: Curves.easeOutCubic,
    ));
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _sectionController.forward();
    });
  }

  @override
  void dispose() {
    _sectionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.blue.withOpacity(0.7),
              Colors.purple.withOpacity(0.7),
              Colors.white.withOpacity(0.1),
            ],
          ),
        ),
        child: FadeTransition(
          opacity: _sectionFade,
          child: SlideTransition(
            position: _sectionSlide,
            child: Center(
              child: Container(
                constraints: const BoxConstraints(maxWidth: 1000),
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: ValueListenableBuilder(
                  valueListenable: Hive.box('SONG_HISTORY').listenable(),
                  builder: (context, box, child) {
                    List songs = box.values.toList();
                    songs.sort((a, b) =>
                        (b['updatedAt'] ?? 0).compareTo((a['updatedAt'] ?? 0)));
                    return Column(
                      children: songs.map((song) {
                        return SwipeActionCell(
                          backgroundColor: Colors.transparent,
                          key: ObjectKey(song['videoId']),
                          trailingActions: <SwipeAction>[
                            SwipeAction(
                                title: "Remove",
                                onTap: (CompletionHandler handler) async {
                                  Modals.showConfirmBottomModal(
                                    context,
                                    message: "Are you sure you want to remove this item?",
                                    isDanger: true,
                                  ).then((bool confirm) async {
                                    if (confirm) {
                                      await box.delete(song['videoId']);
                                    }
                                  });
                                },
                                color: Colors.red),
                          ],
                          child: SongTile(song: song),
                        );
                      }).toList(),
                    );
                  },
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
