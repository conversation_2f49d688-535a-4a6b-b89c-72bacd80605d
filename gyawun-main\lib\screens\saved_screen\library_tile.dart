import 'package:cached_network_image/cached_network_image.dart';
import 'package:expandable_text/expandable_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'dart:ui';

import '../../services/media_player.dart';
import '../../utils/adaptive_widgets/adaptive_widgets.dart';
import '../../utils/bottom_modals.dart';
import '../../utils/extensions.dart';

class LibraryTile extends StatelessWidget {
  const LibraryTile({required this.songs, required this.index, super.key});
  final List songs;
  final int index;

  String _buildSubtitle(Map item) {
    List sub = [];
    if (sub.isEmpty && item['artists'] != null) {
      for (Map artist in item['artists']) {
        sub.add(artist['name']);
      }
    }
    if (sub.isEmpty && item['album'] != null) {
      sub.add(item['album']['name']);
    }
    String s = sub.join(' · ');
    return item['subtitle'] ?? s;
  }

  String _getImageUrl(List thumbnails) {
    try {
      if (thumbnails.isEmpty) {
        return 'https://via.placeholder.com/100x100';
      }

      // Find the highest quality thumbnail available
      var validThumbnails = thumbnails.where((el) =>
        el != null &&
        el is Map &&
        el['width'] != null &&
        el['url'] != null
      ).toList();

      if (validThumbnails.isEmpty) {
        return 'https://via.placeholder.com/100x100';
      }

      // Sort by width to get highest quality
      validThumbnails.sort((a, b) => (b['width'] ?? 0).compareTo(a['width'] ?? 0));

      String url = validThumbnails.first['url'].toString();

      // Enhance URL for better quality
      return url
          .replaceAll('w60-h60', 'w120-h120')
          .replaceAll('w120-h120', 'w150-h150')
          .replaceAll('=w60-h60', '=w150-h150')
          .replaceAll('=w120-h120', '=w150-h150');
    } catch (e) {
      debugPrint('❌ LibraryTile: Error getting image URL: $e');
      return 'https://via.placeholder.com/100x100';
    }
  }

  @override
  Widget build(BuildContext context) {
    if (index >= songs.length || songs[index] == null) {
      return const SizedBox.shrink();
    }

    Map song = songs[index];
    final double height = 50;
    final List thumbnails = song['thumbnails'] ?? [];
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 0),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.10),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ListTile(
              onTap: () async {
                try {
                  // Validate and convert songs to proper Map<String, dynamic> format
                  List<Map<String, dynamic>> validSongs = [];

                  for (var songItem in songs) {
                    if (songItem == null) continue;

                    Map<String, dynamic> safeSong = {};
                    if (songItem is Map<String, dynamic>) {
                      safeSong = Map<String, dynamic>.from(songItem);
                    } else if (songItem is Map) {
                      songItem.forEach((key, value) {
                        if (key != null) {
                          safeSong[key.toString()] = value;
                        }
                      });
                    } else {
                      continue;
                    }

                    // Only add songs with valid videoId
                    if (safeSong['videoId'] != null && safeSong['videoId'].toString().trim().isNotEmpty) {
                      validSongs.add(safeSong);
                    }
                  }

                  if (validSongs.isNotEmpty) {
                    // Find the correct index in the valid songs list
                    int adjustedIndex = 0;
                    String? currentVideoId = song['videoId']?.toString();
                    if (currentVideoId != null) {
                      for (int i = 0; i < validSongs.length; i++) {
                        if (validSongs[i]['videoId']?.toString() == currentVideoId) {
                          adjustedIndex = i;
                          break;
                        }
                      }
                    }

                    await GetIt.I<MediaPlayer>().playAll(validSongs, index: adjustedIndex);
                  }
                } catch (e) {
                  debugPrint('❌ LibraryTile: Error playing song: $e');
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Error playing song: $e'))
                    );
                  }
                }
              },
              onLongPress: () {
                if (song['videoId'] != null) {
                  Modals.showSongBottomModal(context, song);
                }
              },
              leading: ClipRRect(
                borderRadius: BorderRadius.circular(3),
                child: CachedNetworkImage(
                  imageUrl: _getImageUrl(thumbnails),
                  height: height,
                  width: 50,
                  fit: BoxFit.cover,
                  memCacheWidth: 100, // Higher quality cache
                  memCacheHeight: (height * 2.0).round(),
                  maxWidthDiskCache: 150, // Better disk cache
                  maxHeightDiskCache: (height * 3.0).round(),
                  fadeInDuration: const Duration(milliseconds: 150),
                  fadeOutDuration: const Duration(milliseconds: 75),
                  errorWidget: (context, url, error) => Container(
                    width: 50,
                    height: height,
                    color: Colors.grey[300],
                    child: Icon(Icons.music_note, size: 24),
                  ),
                ),
              ),
              title: Text(
                song['title'] ?? "",
                maxLines: 1,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : Colors.black,
                ),
              ),
              subtitle: Text(
                _buildSubtitle(song),
                maxLines: 1,
                style: TextStyle(
                  color: Colors.grey.withAlpha(250),
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
                overflow: TextOverflow.ellipsis,
              ),
              trailing: song['endpoint'] != null && song['videoId'] == null
                  ? const Icon(CupertinoIcons.chevron_right)
                  : null,
            ),
          ),
        ),
      ),
    );
  }
}
