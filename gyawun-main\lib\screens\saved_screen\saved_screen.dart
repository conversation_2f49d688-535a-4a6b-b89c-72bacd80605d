import 'dart:collection';

import 'dart:math';
import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:provider/provider.dart';
import '../../services/library.dart';
import '../../themes/colors.dart';
import '../../utils/adaptive_widgets/adaptive_widgets.dart';
import '../../utils/bottom_modals.dart';
import '../browse_screen/browse_screen.dart';
import 'download_screen.dart';
import 'favourite_details_screen.dart';
import 'history_screen.dart';
import 'playlist_details_screen.dart';

class SavedScreen extends StatefulWidget {
  const SavedScreen({super.key});

  @override
  State<SavedScreen> createState() => _SavedScreenState();
}

class _SavedScreenState extends State<SavedScreen> with SingleTickerProviderStateMixin {
  late AnimationController _sectionController;
  late Animation<double> _sectionFade;
  late Animation<Offset> _sectionSlide;

  @override
  void initState() {
    super.initState();
    _sectionController = AnimationController(
      duration: const Duration(milliseconds: 900),
      vsync: this,
    );
    _sectionFade = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(
      parent: _sectionController,
      curve: Curves.easeIn,
    ));
    _sectionSlide = Tween<Offset>(begin: const Offset(0, 0.08), end: Offset.zero).animate(CurvedAnimation(
      parent: _sectionController,
      curve: Curves.easeOutCubic,
    ));
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _sectionController.forward();
    });
  }

  @override
  void dispose() {
    _sectionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Map playlists = context.watch<LibraryService>().playlists;
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              iOSBlue.withOpacity(0.7),
              iOSPurple.withOpacity(0.7),
              Colors.white.withOpacity(0.1),
            ],
          ),
        ),
        child: FadeTransition(
          opacity: _sectionFade,
          child: SlideTransition(
            position: _sectionSlide,
            child: SingleChildScrollView(
              child: Center(
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 1000),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Column(
                    children: [
                      // Professional header section
                      Container(
                        margin: const EdgeInsets.only(bottom: 24),
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              iOSPurple.withValues(alpha: 0.1),
                              iOSBlue.withValues(alpha: 0.1),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(iOSCardRadius),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.1),
                            width: 0.5,
                          ),
                        ),
                        child: Row(
                          children: [
                            Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [iOSPurple, iOSBlue],
                                ),
                                borderRadius: BorderRadius.circular(30),
                              ),
                              child: const Icon(
                                CupertinoIcons.music_albums_fill,
                                color: Colors.white,
                                size: 30,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "Your Music Library",
                                    style: TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.w700,
                                      color: Theme.of(context).brightness == Brightness.dark
                                          ? Colors.white
                                          : Colors.black,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    "All your saved music in one place",
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w400,
                                      color: Theme.of(context).brightness == Brightness.dark
                                          ? Colors.white.withValues(alpha: 0.7)
                                          : Colors.black.withValues(alpha: 0.7),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Favourites section
                      Container(
                        margin: const EdgeInsets.only(bottom: 12),
                        decoration: BoxDecoration(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withValues(alpha: 0.05)
                              : Colors.white,
                          borderRadius: BorderRadius.circular(iOSCardRadius),
                          border: Border.all(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.white.withValues(alpha: 0.1)
                                : Colors.black.withValues(alpha: 0.05),
                            width: 0.5,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.05),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: AdaptiveListTile(
                          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          title: Text(
                            "Favourites",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.white
                                  : Colors.black,
                            ),
                          ),
                          leading: Container(
                            height: 50,
                            width: 50,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [iOSRed, iOSOrange],
                              ),
                              borderRadius: BorderRadius.circular(iOSSmallRadius),
                            ),
                            child: const Icon(
                              CupertinoIcons.heart_fill,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                          subtitle: ValueListenableBuilder(
                            valueListenable: Hive.box('FAVOURITES').listenable(),
                            builder: (context, box, child) {
                              return Text(
                                "${box.length} songs",
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Colors.white.withValues(alpha: 0.7)
                                      : Colors.black.withValues(alpha: 0.7),
                                ),
                              );
                            },
                          ),
                          trailing: Icon(
                            CupertinoIcons.chevron_right,
                            size: 18,
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.white.withValues(alpha: 0.6)
                                : Colors.black.withValues(alpha: 0.6),
                          ),
                          onTap: () => Navigator.push(
                              context,
                              AdaptivePageRoute.create(
                                (context) => const FavouriteDetailsScreen(),
                              )),
                        ),
                      ),

                      // Downloads section
                      Container(
                        margin: const EdgeInsets.only(bottom: 12),
                        decoration: BoxDecoration(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withValues(alpha: 0.05)
                              : Colors.white,
                          borderRadius: BorderRadius.circular(iOSCardRadius),
                          border: Border.all(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.white.withValues(alpha: 0.1)
                                : Colors.black.withValues(alpha: 0.05),
                            width: 0.5,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.05),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: AdaptiveListTile(
                          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          title: Text(
                            "Downloads",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.white
                                  : Colors.black,
                            ),
                          ),
                          leading: Container(
                            height: 50,
                            width: 50,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [iOSGreen, iOSBlue],
                              ),
                              borderRadius: BorderRadius.circular(iOSSmallRadius),
                            ),
                            child: const Icon(
                              CupertinoIcons.cloud_download_fill,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                          subtitle: ValueListenableBuilder(
                            valueListenable: Hive.box('DOWNLOADS').listenable(),
                            builder: (context, box, child) {
                              List values = box.values.toList();
                              List downloaded = values.where((element) => element['status'] == 'DOWNLOADED').toList();
                              return Text(
                                "${downloaded.length} songs",
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Colors.white.withValues(alpha: 0.7)
                                      : Colors.black.withValues(alpha: 0.7),
                                ),
                              );
                            },
                          ),
                          trailing: Icon(
                            CupertinoIcons.chevron_right,
                            size: 18,
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.white.withValues(alpha: 0.6)
                                : Colors.black.withValues(alpha: 0.6),
                          ),
                          onTap: () => Navigator.push(
                              context,
                              AdaptivePageRoute.create(
                                (context) => const DownloadScreen(),
                              )),
                        ),
                      ),

                      // History section
                      Container(
                        margin: const EdgeInsets.only(bottom: 24),
                        decoration: BoxDecoration(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withValues(alpha: 0.05)
                              : Colors.white,
                          borderRadius: BorderRadius.circular(iOSCardRadius),
                          border: Border.all(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.white.withValues(alpha: 0.1)
                                : Colors.black.withValues(alpha: 0.05),
                            width: 0.5,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.05),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: AdaptiveListTile(
                          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          title: Text(
                            "History",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.white
                                  : Colors.black,
                            ),
                          ),
                          leading: Container(
                            height: 50,
                            width: 50,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [iOSPurple, iOSOrange],
                              ),
                              borderRadius: BorderRadius.circular(iOSSmallRadius),
                            ),
                            child: const Icon(
                              CupertinoIcons.clock_fill,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                          subtitle: ValueListenableBuilder(
                            valueListenable: Hive.box('SONG_HISTORY').listenable(),
                            builder: (context, box, child) {
                              return Text(
                                "${box.length} songs",
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Colors.white.withValues(alpha: 0.7)
                                      : Colors.black.withValues(alpha: 0.7),
                                ),
                              );
                            },
                          ),
                          trailing: Icon(
                            CupertinoIcons.chevron_right,
                            size: 18,
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.white.withValues(alpha: 0.6)
                                : Colors.black.withValues(alpha: 0.6),
                          ),
                          onTap: () => Navigator.push(
                              context,
                              AdaptivePageRoute.create(
                                (context) => const HistoryScreen(),
                              )),
                        ),
                      ),

                      // Playlists section header
                      if (playlists.isNotEmpty) ...[
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 16),
                          child: Row(
                            children: [
                              Text(
                                "Your Playlists",
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w700,
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Colors.white
                                      : Colors.black,
                                ),
                              ),
                              const Spacer(),
                              Text(
                                "${playlists.length} playlists",
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Colors.white.withValues(alpha: 0.6)
                                      : Colors.black.withValues(alpha: 0.6),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],

                      // Playlists
                      ...SplayTreeMap.from(playlists)
                          .map((key, item) {
                            return MapEntry(
                              key,
                              item == null
                                  ? const SizedBox.shrink()
                                  : Container(
                                      margin: const EdgeInsets.only(bottom: 8),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).brightness == Brightness.dark
                                            ? Colors.white.withValues(alpha: 0.05)
                                            : Colors.white,
                                        borderRadius: BorderRadius.circular(iOSCardRadius),
                                        border: Border.all(
                                          color: Theme.of(context).brightness == Brightness.dark
                                              ? Colors.white.withValues(alpha: 0.1)
                                              : Colors.black.withValues(alpha: 0.05),
                                          width: 0.5,
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withValues(alpha: 0.05),
                                            blurRadius: 8,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: AdaptiveListTile(
                                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                                        onSecondaryTap: () {
                                          if (item['videoId'] == null &&
                                              item['playlistId'] != null) {
                                            Modals.showPlaylistBottomModal(
                                                context, item);
                                          } else if (item['isPredefined'] == false) {
                                            Modals.showPlaylistBottomModal(context,
                                                {...item, 'playlistId': key});
                                          }
                                        },
                                        onLongPress: () {
                                          if (item['videoId'] == null &&
                                              item['playlistId'] != null) {
                                            Modals.showPlaylistBottomModal(
                                                context, item);
                                          } else if (item['isPredefined'] == false) {
                                            Modals.showPlaylistBottomModal(context,
                                                {...item, 'playlistId': key});
                                          }
                                        },
                                        onTap: () {
                                          if (item['isPredefined']) {
                                            Navigator.push(
                                              context,
                                              AdaptivePageRoute.create(
                                                (context) => BrowseScreen(
                                                    endpoint: item['endpoint']
                                                        .cast<String, dynamic>()),
                                              ),
                                            );
                                          } else {
                                            Navigator.push(
                                              context,
                                              AdaptivePageRoute.create(
                                                (context) => PlaylistDetailsScreen(
                                                    playlistkey: key),
                                              ),
                                            );
                                          }
                                        },
                                        title: Text(
                                          item['title'],
                                          maxLines: 2,
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w600,
                                            color: Theme.of(context).brightness == Brightness.dark
                                                ? Colors.white
                                                : Colors.black,
                                          ),
                                        ),
                                        leading: item['isPredefined'] == true ||
                                                (item['songs'] != null &&
                                                    item['songs']?.length > 0)
                                            ? ClipRRect(
                                                borderRadius: BorderRadius.circular(
                                                    item['type'] == 'ARTIST'
                                                        ? 25
                                                        : iOSSmallRadius),
                                                child: item['isPredefined'] == true
                                                    ? CachedNetworkImage(
                                                        imageUrl: item['thumbnails']
                                                            .first['url']
                                                            .replaceAll('w540-h225',
                                                                'w60-h60'),
                                                        height: 50,
                                                        width: 50,
                                                        fit: BoxFit.cover,
                                                      )
                                                    : SizedBox(
                                                        height: 50,
                                                        width: 50,
                                                        child: StaggeredGrid.count(
                                                          crossAxisCount:
                                                              item['songs'].length > 1
                                                                  ? 2
                                                                  : 1,
                                                          children: (item['songs']
                                                                          as List)
                                                              .sublist(
                                                                  0,
                                                                  min(
                                                                      item['songs']
                                                                          .length,
                                                                      4))
                                                              .indexed
                                                              .map((ind) {
                                                            int index = ind.$1;
                                                            Map song = ind.$2;
                                                            return CachedNetworkImage(
                                                              imageUrl:
                                                                  song['thumbnails']
                                                                      .first['url']
                                                                      .replaceAll(
                                                                          'w540-h225',
                                                                          'w60-h60'),
                                                              height: (item['songs']
                                                                      .length <=
                                                                  2 ||
                                                                  (item['songs']
                                                                              .length ==
                                                                          3 &&
                                                                      index == 0))
                                                                  ? 50
                                                                  : null,
                                                              fit: BoxFit.cover,
                                                            );
                                                          }).toList(),
                                                        ),
                                                      ),
                                              )
                                            : Container(
                                                height: 50,
                                                width: 50,
                                                decoration: BoxDecoration(
                                                  gradient: LinearGradient(
                                                    colors: [iOSBlue, iOSPurple],
                                                  ),
                                                  borderRadius: BorderRadius.circular(iOSSmallRadius),
                                                ),
                                                child: const Icon(
                                                  CupertinoIcons.music_note_list,
                                                  color: Colors.white,
                                                  size: 24,
                                                ),
                                              ),
                                        subtitle: (item['songs'] != null ||
                                                item['isPredefined'])
                                            ? Text(
                                                item['isPredefined'] == true
                                                    ? item['subtitle']
                                                    : "${item['songs'].length} songs",
                                                maxLines: 1,
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  color: Theme.of(context).brightness == Brightness.dark
                                                      ? Colors.white.withValues(alpha: 0.7)
                                                      : Colors.black.withValues(alpha: 0.7),
                                                ),
                                              )
                                            : null,
                                        trailing: Icon(
                                          CupertinoIcons.chevron_right,
                                          size: 18,
                                          color: Theme.of(context).brightness == Brightness.dark
                                              ? Colors.white.withValues(alpha: 0.6)
                                              : Colors.black.withValues(alpha: 0.6),
                                        ),
                                      ),
                                    ),
                            );
                          })
                          .values,
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
