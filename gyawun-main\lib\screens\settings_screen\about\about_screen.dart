import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:ui';

import '../../../app_config.dart';
import '../../../themes/colors.dart';
import '../../../utils/adaptive_widgets/adaptive_widgets.dart';

class AboutScreen extends StatefulWidget {
  const AboutScreen({super.key});

  @override
  State<AboutScreen> createState() => _AboutScreenState();
}

class _AboutScreenState extends State<AboutScreen> with SingleTickerProviderStateMixin {
  late AnimationController _sectionController;
  late Animation<double> _sectionFade;
  late Animation<Offset> _sectionSlide;

  @override
  void initState() {
    super.initState();
    _sectionController = AnimationController(
      duration: const Duration(milliseconds: 900),
      vsync: this,
    );
    _sectionFade = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(
      parent: _sectionController,
      curve: Curves.easeIn,
    ));
    _sectionSlide = Tween<Offset>(begin: const Offset(0, 0.08), end: Offset.zero).animate(CurvedAnimation(
      parent: _sectionController,
      curve: Curves.easeOutCubic,
    ));
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _sectionController.forward();
    });
  }

  @override
  void dispose() {
    _sectionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              iOSBlue.withOpacity(0.7),
              iOSPurple.withOpacity(0.7),
              Colors.white.withOpacity(0.1),
            ],
          ),
        ),
        child: FadeTransition(
          opacity: _sectionFade,
          child: SlideTransition(
            position: _sectionSlide,
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              children: [
                // App Header Section
                Container(
                  margin: const EdgeInsets.only(bottom: 32),
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        iOSBlue.withValues(alpha: 0.1),
                        iOSPurple.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(iOSCardRadius),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.1),
                      width: 0.5,
                    ),
                  ),
                  child: Column(
                    children: [
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [iOSBlue, iOSPurple],
                          ),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: iOSBlue.withValues(alpha: 0.3),
                              blurRadius: 15,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: const Icon(
                          CupertinoIcons.music_note,
                          color: Colors.white,
                          size: 40,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        "CODO",
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.w800,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black,
                          letterSpacing: 2.0,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        "Best Music Streaming Platform",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withValues(alpha: 0.8)
                              : Colors.black.withValues(alpha: 0.8),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        "Version ${appConfig.codeName}",
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withValues(alpha: 0.6)
                              : Colors.black.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                ),

                // Contact Information
                _buildContactCard(
                  context,
                  "Developer",
                  "uniqueshiwakoti",
                  CupertinoIcons.person_fill,
                  [iOSGreen, iOSBlue],
                  () => launchUrl(
                      Uri.parse('https://instagram.com/uniqueshiwakoti5'),
                      mode: LaunchMode.externalApplication),
                ),

                _buildContactCard(
                  context,
                  "Organization",
                  "CODOVO STUDIO",
                  CupertinoIcons.building_2_fill,
                  [iOSOrange, iOSRed],
                  () => launchUrl(
                      Uri.parse('https://instagram.com/codovostudio'),
                      mode: LaunchMode.externalApplication),
                ),

                _buildContactCard(
                  context,
                  "Website",
                  "codovostudio.netlify.app",
                  CupertinoIcons.globe,
                  [iOSBlue, iOSPurple],
                  () => launchUrl(
                      Uri.parse('https://codovostudio.netlify.app'),
                      mode: LaunchMode.externalApplication),
                ),

                _buildContactCard(
                  context,
                  "Contact Email",
                  "<EMAIL>",
                  CupertinoIcons.mail_solid,
                  [iOSPurple, iOSOrange],
                  () => launchUrl(
                      Uri.parse('mailto:<EMAIL>'),
                      mode: LaunchMode.externalApplication),
                ),

                // Made in Nepal
                Container(
                  margin: const EdgeInsets.only(top: 32, bottom: 16),
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [iOSGreen.withValues(alpha: 0.1), iOSBlue.withValues(alpha: 0.1)],
                    ),
                    borderRadius: BorderRadius.circular(iOSCardRadius),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.1),
                      width: 0.5,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [iOSGreen, iOSBlue],
                          ),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Icon(
                          CupertinoIcons.location_solid,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Text(
                        "Made with ❤️ in Nepal",
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContactCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    List<Color> gradientColors,
    VoidCallback onTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.white.withValues(alpha: 0.05)
            : Colors.white,
        borderRadius: BorderRadius.circular(iOSCardRadius),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.white.withValues(alpha: 0.1)
              : Colors.black.withValues(alpha: 0.05),
          width: 0.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: AdaptiveListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: gradientColors),
            borderRadius: BorderRadius.circular(25),
          ),
          child: Icon(
            icon,
            color: Colors.white,
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.white
                : Colors.black,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.white.withValues(alpha: 0.7)
                : Colors.black.withValues(alpha: 0.7),
          ),
        ),
        trailing: Icon(
          CupertinoIcons.chevron_right,
          size: 18,
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.white.withValues(alpha: 0.6)
              : Colors.black.withValues(alpha: 0.6),
        ),
        onTap: onTap,
      ),
    );
  }
}
