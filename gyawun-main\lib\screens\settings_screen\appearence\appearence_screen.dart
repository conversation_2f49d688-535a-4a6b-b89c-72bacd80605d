import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'dart:ui';

import '../../../themes/colors.dart';
import '../../../themes/text_styles.dart';
import '../../../utils/adaptive_widgets/adaptive_widgets.dart';
import '../color_icon.dart';
import 'appearence_screen_data.dart';

class AppearenceScreen extends StatefulWidget {
  const AppearenceScreen({super.key});

  @override
  State<AppearenceScreen> createState() => _AppearenceScreenState();
}

class _AppearenceScreenState extends State<AppearenceScreen> with SingleTickerProviderStateMixin {
  late AnimationController _sectionController;
  late Animation<double> _sectionFade;
  late Animation<Offset> _sectionSlide;

  @override
  void initState() {
    super.initState();
    _sectionController = AnimationController(
      duration: const Duration(milliseconds: 900),
      vsync: this,
    );
    _sectionFade = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(
      parent: _sectionController,
      curve: Curves.easeIn,
    ));
    _sectionSlide = Tween<Offset>(begin: const Offset(0, 0.08), end: Offset.zero).animate(CurvedAnimation(
      parent: _sectionController,
      curve: Curves.easeOutCubic,
    ));
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _sectionController.forward();
    });
  }

  @override
  void dispose() {
    _sectionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              iOSBlue.withOpacity(0.7),
              iOSPurple.withOpacity(0.7),
              Colors.white.withOpacity(0.1),
            ],
          ),
        ),
        child: FadeTransition(
          opacity: _sectionFade,
          child: SlideTransition(
            position: _sectionSlide,
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              children: [
                ...appearenceScreenData(context).map((e) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: AdaptiveListTile(
                      title: Text(
                        e.title,
                        style: textStyle(context, bold: false)
                            .copyWith(fontSize: 16),
                      ),
                      leading: (e.icon != null)
                          ? ColorIcon(
                              color: e.color,
                              icon: e.icon!,
                            )
                          : null,
                      trailing: e.trailing != null
                          ? e.trailing!(context)
                          : (e.hasNavigation
                              ? Icon(
                                  AdaptiveIcons.chevron_right,
                                  size: 30,
                                )
                              : null),
                      onTap: (e.hasNavigation && e.location != null) ||
                              e.onTap != null
                          ? () {
                              if (e.hasNavigation && e.location != null) {
                                context.go(e.location!);
                              } else if (e.onTap != null) {
                                e.onTap!(context);
                              }
                            }
                          : null,
                      subtitle: e.subtitle != null ? e.subtitle!(context) : null,
                    ),
                  );
                }),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
