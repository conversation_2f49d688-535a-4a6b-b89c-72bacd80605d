import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:provider/provider.dart';


import '../../../services/settings_manager.dart';
import '../../../themes/text_styles.dart';
import '../../../utils/adaptive_widgets/adaptive_widgets.dart';
import '../../../utils/bottom_modals.dart';
import '../../../ytmusic/ytmusic.dart';
import '../setting_item.dart';

Box _box = Hive.box('SETTINGS');

List<SettingItem> contentScreenData(BuildContext context) => [
      SettingItem(
        title: "Country",
        icon: CupertinoIcons.placemark,
        hasNavigation: false,
        trailing: (context) {
          return AdaptiveDropdownButton(
            value: context.watch<SettingsManager>().location,
            items: context
                .read<SettingsManager>()
                .locations
                .map(
                  (location) => AdaptiveDropdownMenuItem(
                    value: location,
                    child: Text(
                      textAlign: TextAlign.right,
                      location['name']!,
                      style: smallTextStyle(context),
                      maxLines: 2,
                    ),
                  ),
                )
                .toList(),
            onChanged: (location) {
              context.read<SettingsManager>().location = location!;
            },
          );
        },
      ),
      SettingItem(
        title: "Language",
        icon: CupertinoIcons.globe,
        trailing: (context) {
          return AdaptiveDropdownButton(
            value: context.watch<SettingsManager>().language,
            items: context
                .read<SettingsManager>()
                .languages
                .map(
                  (language) => AdaptiveDropdownMenuItem(
                    value: language,
                    child: Text(
                      language['name']!,
                      style: smallTextStyle(context),
                      textAlign: TextAlign.end,
                    ),
                  ),
                )
                .toList(),
            onChanged: (language) {
              context.read<SettingsManager>().language = language!;
            },
          );
        },
      ),
      SettingItem(
        title: "Personalised Content",
        icon: Icons.recommend_outlined,
        onTap: (context) async {
          Modals.showCenterLoadingModal(context);
          bool isEnabled = _box.get('PERSONALISED_CONTENT', defaultValue: true);
          await _box.put('PERSONALISED_CONTENT', !isEnabled);
          await GetIt.I<YTMusic>().resetVisitorId();

          if (context.mounted) {
            context.pop();
          }
        },
        trailing: (context) {
          return ValueListenableBuilder(
            valueListenable: _box.listenable(keys: ['PERSONALISED_CONTENT']),
            builder: (context, value, child) {
              return AdaptiveSwitch(
                value: value.get('PERSONALISED_CONTENT', defaultValue: true),
                onChanged: (val) async {
                  Modals.showCenterLoadingModal(context);
                  await value.put('PERSONALISED_CONTENT', val);
                  await GetIt.I<YTMusic>().resetVisitorId();

                  if (context.mounted) {
                    context.pop();
                  }
                },
              );
            },
          );
        },
      ),

    ];
