import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:just_audio/just_audio.dart';
import 'package:provider/provider.dart';
import 'dart:ui';

import '../../../services/media_player.dart';
import '../../../services/settings_manager.dart';
import '../../../themes/colors.dart';
import '../../../themes/text_styles.dart';
import '../../../utils/adaptive_widgets/adaptive_widgets.dart';

class EqualizerScreen extends StatefulWidget {
  const EqualizerScreen({super.key});

  @override
  State<EqualizerScreen> createState() => _EqualizerScreenState();
}

class _EqualizerScreenState extends State<EqualizerScreen> with SingleTickerProviderStateMixin {
  late AnimationController _sectionController;
  late Animation<double> _sectionFade;
  late Animation<Offset> _sectionSlide;

  @override
  void initState() {
    super.initState();
    _sectionController = AnimationController(
      duration: const Duration(milliseconds: 900),
      vsync: this,
    );
    _sectionFade = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(
      parent: _sectionController,
      curve: Curves.easeIn,
    ));
    _sectionSlide = Tween<Offset>(begin: const Offset(0, 0.08), end: Offset.zero).animate(CurvedAnimation(
      parent: _sectionController,
      curve: Curves.easeOutCubic,
    ));
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _sectionController.forward();
    });
  }

  @override
  void dispose() {
    _sectionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    SettingsManager settingsManager = context.watch<SettingsManager>();
    return ClipRRect(
      borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20), topRight: Radius.circular(20)),
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            "Loudness & Equalizer",
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w700,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.black,
            ),
          ),
          centerTitle: true,
        ),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                iOSBlue.withOpacity(0.7),
                iOSPurple.withOpacity(0.7),
                Colors.white.withOpacity(0.1),
              ],
            ),
          ),
          child: FadeTransition(
            opacity: _sectionFade,
            child: SlideTransition(
              position: _sectionSlide,
              child: Center(
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 1000),
                  child: ListView(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    children: [
                      Container(
                        margin: const EdgeInsets.only(bottom: 12),
                        decoration: BoxDecoration(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withValues(alpha: 0.05)
                              : Colors.white,
                          borderRadius: BorderRadius.circular(iOSCardRadius),
                          border: Border.all(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.white.withValues(alpha: 0.1)
                                : Colors.black.withValues(alpha: 0.05),
                            width: 0.5,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.05),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: AdaptiveListTile(
                          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          title: Text(
                            "Loudness Enhancer",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.white
                                  : Colors.black,
                            ),
                          ),
                          trailing: AdaptiveSwitch(
                            value: settingsManager.loudnessEnabled,
                            onChanged: (value) async {
                              await GetIt.I<MediaPlayer>().setLoudnessEnabled(value);
                            },
                          ),
                          onTap: () async {
                            await GetIt.I<MediaPlayer>()
                                .setLoudnessEnabled(!settingsManager.loudnessEnabled);
                          },
                        ),
                      ),
                      Container(
                        margin: const EdgeInsets.only(bottom: 12),
                        decoration: BoxDecoration(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withValues(alpha: 0.05)
                              : Colors.white,
                          borderRadius: BorderRadius.circular(iOSCardRadius),
                          border: Border.all(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.white.withValues(alpha: 0.1)
                                : Colors.black.withValues(alpha: 0.05),
                            width: 0.5,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.05),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: LoudnessControls(
                              disabled: settingsManager.loudnessEnabled == false),
                        ),
                      ),
                      Container(
                        margin: const EdgeInsets.only(bottom: 12),
                        decoration: BoxDecoration(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withValues(alpha: 0.05)
                              : Colors.white,
                          borderRadius: BorderRadius.circular(iOSCardRadius),
                          border: Border.all(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.white.withValues(alpha: 0.1)
                                : Colors.black.withValues(alpha: 0.05),
                            width: 0.5,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.05),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: AdaptiveListTile(
                          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          title: Text(
                            "Enable Equalizer",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.white
                                  : Colors.black,
                            ),
                          ),
                          trailing: AdaptiveSwitch(
                            value: settingsManager.equalizerEnabled,
                            onChanged: (value) async {
                              await GetIt.I<MediaPlayer>().setEqualizerEnabled(value);
                            },
                          ),
                          onTap: () async {
                            await GetIt.I<MediaPlayer>().setEqualizerEnabled(
                                !settingsManager.equalizerEnabled);
                          },
                        ),
                      ),
                      Container(
                        margin: const EdgeInsets.only(bottom: 12),
                        decoration: BoxDecoration(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withValues(alpha: 0.05)
                              : Colors.white,
                          borderRadius: BorderRadius.circular(iOSCardRadius),
                          border: Border.all(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.white.withValues(alpha: 0.1)
                                : Colors.black.withValues(alpha: 0.05),
                            width: 0.5,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.05),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: SizedBox(
                            height: 400,
                            child: EqualizerControls(
                              disabled: !settingsManager.equalizerEnabled,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class EqualizerControls extends StatelessWidget {
  const EqualizerControls({
    this.disabled = false,
    super.key,
  });
  final bool disabled;

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: getEqParms(),
      builder: (context, snapshot) {
        final parameters = snapshot.data;
        if (parameters == null) return const SizedBox();

        return ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 400),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              for (var band in parameters['bands'])
                Expanded(
                  child: Column(
                    children: [
                      Expanded(
                        child: VerticalSlider(
                          min: parameters['minDecibels'],
                          max: parameters['maxDecibels'],
                          value: band['gain'],
                          bandIndex: band['index'] as int,
                          disabled: disabled,
                          centerFrequency: (band['centerFrequency'] as num).round(),
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}

class LoudnessControls extends StatelessWidget {
  const LoudnessControls({this.disabled = false, super.key});
  final bool disabled;
  @override
  Widget build(BuildContext context) {
    return Slider(
      min: -1,
      max: 1,
      value: context.watch<SettingsManager>().loudnessTargetGain,
      onChanged: disabled
          ? null
          : (val) async {
              await GetIt.I<MediaPlayer>().setLoudnessTargetGain(val);
            },
      label: context.watch<SettingsManager>().loudnessTargetGain.toString(),
    );
  }
}

class VerticalSlider extends StatefulWidget {
  final double value;
  final double min;
  final double max;
  final int bandIndex;
  final bool disabled;
  final int centerFrequency;

  const VerticalSlider({
    super.key,
    required this.value,
    this.min = 0.0,
    this.max = 1.0,
    required this.bandIndex,
    required this.centerFrequency,
    this.disabled = false,
  });

  @override
  State<VerticalSlider> createState() => _VerticalSliderState();
}

class _VerticalSliderState extends State<VerticalSlider> {
  double? sliderValue;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text((sliderValue ?? widget.value).toStringAsFixed(2)),
        Expanded(
          child: AdaptiveSlider(
            value: sliderValue ?? widget.value,
            min: widget.min,
            max: widget.max,
            disabled: widget.disabled,
            vertical: true,
            onChanged: (val) {
              setState(() {
                sliderValue = val;
                setGain(widget.bandIndex, val);
              });
            },
          ),
        ),
        Text('${widget.centerFrequency} Hz'),
      ],
    );
  }
}

Future<Map> getEqParms() async {
  AndroidEqualizerParameters equalizerParams =
      await GetIt.I<AndroidEqualizer>().parameters;
  final List<AndroidEqualizerBand> bands = equalizerParams.bands;
  final List<Map> bandList = bands
      .map(
        (e) => {
          'centerFrequency': e.centerFrequency,
          'gain': e.gain,
          'index': e.index,
        },
      )
      .toList();

  return {
    'maxDecibels': equalizerParams.maxDecibels,
    'minDecibels': equalizerParams.minDecibels,
    'bands': bandList
  };
}

void setGain(int bandIndex, double gain) async {
  await GetIt.I<SettingsManager>().setEqualizerBandsGain(bandIndex, gain);
  AndroidEqualizerParameters params =
      await GetIt.I<AndroidEqualizer>().parameters;
  await params.bands[bandIndex].setGain(gain);
}
