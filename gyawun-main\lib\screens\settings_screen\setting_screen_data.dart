import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';


import 'appearence/appearence_screen_data.dart';
import 'content/content_screen_data.dart';
import 'playback/audio_and_playback_screen_data.dart';
import 'setting_item.dart';

List<SettingItem> settingScreenData(BuildContext context) => [
      SettingItem(
        title: "Profile",
        icon: CupertinoIcons.person_circle_fill,
        color: Colors.accents[6],
        hasNavigation: true,
        location: '/settings/profile',
      ),
      SettingItem(
        title: "Visuals",
        icon: Icons.looks,
        color: Colors.accents[0],
        hasNavigation: true,
        location: '/settings/appearence',
      ),
      SettingItem(
        title: "Database",
        icon: CupertinoIcons.music_note_list,
        color: Colors.accents[1],
        hasNavigation: true,
        location: '/settings/content',
      ),
      SettingItem(
        title: "Audio Customs",
        icon: CupertinoIcons.music_note,
        color: Colors.accents[2],
        hasNavigation: true,
        location: '/settings/playback',
      ),
      SettingItem(
        title: "Backup your data",
        icon: Icons.settings_backup_restore_outlined,
        color: Colors.accents[3],
        hasNavigation: true,
        location: '/settings/backup_restore',
      ),
      SettingItem(
        title: "About",
        icon: Icons.info_rounded,
        color: Colors.accents[4],
        hasNavigation: true,
        location: '/settings/about',
      ),
    ];
List<SettingItem> allSettingsData(BuildContext context) => [
      ...settingScreenData(context),
      ...appearenceScreenData(context),
      ...contentScreenData(context),
      ...audioandplaybackScreenData(context)
    ];
