import 'dart:io';
import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import 'package:url_launcher/url_launcher.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent_ui;

import '../../themes/colors.dart';
import '../../utils/adaptive_widgets/adaptive_widgets.dart';
import '../../utils/bottom_modals.dart';
import 'setting_screen_data.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> with SingleTickerProviderStateMixin {
  TextEditingController searchController = TextEditingController();

  String searchText = "";

  late AnimationController _sectionController;
  late Animation<double> _sectionFade;
  late Animation<Offset> _sectionSlide;

  @override
  void initState() {
    super.initState();
    _sectionController = AnimationController(
      duration: const Duration(milliseconds: 900),
      vsync: this,
    );
    _sectionFade = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(
      parent: _sectionController,
      curve: Curves.easeIn,
    ));
    _sectionSlide = Tween<Offset>(begin: const Offset(0, 0.08), end: Offset.zero).animate(CurvedAnimation(
      parent: _sectionController,
      curve: Curves.easeOutCubic,
    ));
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _sectionController.forward();
    });
  }

  @override
  void dispose() {
    _sectionController.dispose();
    super.dispose();
    searchController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AdaptiveScaffold(
      appBar: AdaptiveAppBar(
        title: Text("Settings",
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w700,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.black,
            )),
        centerTitle: true,
        automaticallyImplyLeading: false,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              iOSBlue.withOpacity(0.7),
              iOSPurple.withOpacity(0.7),
              Colors.white.withOpacity(0.1),
            ],
          ),
        ),
        child: FadeTransition(
          opacity: _sectionFade,
          child: SlideTransition(
            position: _sectionSlide,
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              children: [
                // Professional header section
                Container(
                  margin: const EdgeInsets.only(bottom: 24),
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        iOSBlue.withValues(alpha: 0.1),
                        iOSPurple.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(iOSCardRadius),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.1),
                      width: 0.5,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [iOSBlue, iOSPurple],
                          ),
                          borderRadius: BorderRadius.circular(30),
                        ),
                        child: const Icon(
                          CupertinoIcons.gear_alt,
                          color: Colors.white,
                          size: 30,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "CODO Settings",
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.w700,
                                color: Theme.of(context).brightness == Brightness.dark
                                    ? Colors.white
                                    : Colors.black,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              "Customize your music experience",
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: Theme.of(context).brightness == Brightness.dark
                                    ? Colors.white.withValues(alpha: 0.7)
                                    : Colors.black.withValues(alpha: 0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // Settings list
                ...(searchText == ""
                        ? settingScreenData(context)
                        : allSettingsData(context)
                            .where((element) => element.title
                                .toLowerCase()
                                .contains(searchText.toLowerCase()))
                            .toList())
                    .map((e) {
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    decoration: BoxDecoration(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withValues(alpha: 0.05)
                          : Colors.white,
                      borderRadius: BorderRadius.circular(iOSCardRadius),
                      border: Border.all(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.white.withValues(alpha: 0.1)
                            : Colors.black.withValues(alpha: 0.05),
                        width: 0.5,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: AdaptiveListTile(
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      title: Text(
                        e.title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black,
                        ),
                      ),
                      leading: (e.icon != null)
                          ? Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: e.color?.withValues(alpha: 0.2) ?? iOSBlue.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Icon(
                                e.icon!,
                                color: e.color ?? iOSBlue,
                                size: 22,
                              ),
                            )
                          : null,
                      trailing: e.trailing != null
                          ? e.trailing!(context)
                          : (e.hasNavigation
                              ? Icon(
                                  CupertinoIcons.chevron_right,
                                  size: 18,
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Colors.white.withValues(alpha: 0.6)
                                      : Colors.black.withValues(alpha: 0.6),
                                )
                              : null),
                      onTap: () {
                        if (e.hasNavigation && e.location != null) {
                          context.go(e.location!);
                        } else if (e.onTap != null) {
                          e.onTap!(context);
                        }
                      },
                      subtitle: e.subtitle != null ? e.subtitle!(context) : null,
                    ),
                  );
                }),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

showPaymentsModal(BuildContext context) {
  Widget title = AdaptiveListTile(
    contentPadding: EdgeInsets.zero,
    title: Text(
      "Support CODO",
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.white
            : Colors.black,
      ),
    ),
    leading: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          height: 40,
          width: 40,
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [iOSBlue, iOSPurple]),
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Icon(
            CupertinoIcons.heart_fill,
            color: Colors.white,
            size: 20,
          ),
        ),
      ],
    ),
  );
  Widget child = Column(
    mainAxisSize: MainAxisSize.min,
    children: [
      Container(
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.white.withValues(alpha: 0.05)
              : Colors.white,
          borderRadius: BorderRadius.circular(iOSCardRadius),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.1),
            width: 0.5,
          ),
        ),
        child: AdaptiveListTile(
          contentPadding: const EdgeInsets.all(16),
          leading: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Container(
                width: 40,
                height: 40,
                color: iOSGreen,
                child: const Icon(
                  CupertinoIcons.money_dollar_circle_fill,
                  color: Colors.white,
                ),
              )),
          title: Text(
            "Support Development",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.black,
            ),
          ),
          subtitle: Text(
            "Help us improve CODO",
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white.withValues(alpha: 0.7)
                  : Colors.black.withValues(alpha: 0.7),
            ),
          ),
          onTap: () async {
            Navigator.pop(context);
            await launchUrl(
              Uri.parse('https://codovostudio.netlify.app'),
              mode: LaunchMode.externalApplication,
            );
          },
        ),
      ),
    ],
  );
  if (Platform.isWindows) {
    return fluent_ui.showDialog(
      context: context,
      useRootNavigator: false,
      barrierDismissible: true,
      builder: (context) => BottomModalLayout(title: title, child: child),
    );
  }
  showModalBottomSheet(
    useSafeArea: true,
    backgroundColor: Colors.transparent,
    context: context,
    builder: (context) => BottomModalLayout(title: title, child: child),
  );
}
