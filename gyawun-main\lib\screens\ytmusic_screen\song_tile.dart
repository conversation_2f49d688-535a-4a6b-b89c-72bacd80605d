import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:get_it/get_it.dart';

import '../../services/media_player.dart';
import '../../utils/adaptive_widgets/adaptive_widgets.dart';
import '../browse_screen/browse_screen.dart';

class YTMSongTile extends StatelessWidget {
  const YTMSongTile(
      {required this.items,
      required this.index,
      this.mainBrowse = false,
      super.key});
  final List items;
  final int index;
  final bool mainBrowse;
  @override
  Widget build(BuildContext context) {
    if (index >= items.length || items[index] == null) {
      return const SizedBox.shrink();
    }

    Map item = items[index];
    return AdaptiveListTile(
      margin: const EdgeInsets.symmetric(vertical: 4),
      onTap: () {
        try {
          if (item['videoId'] != null) {
            // Validate and convert songs to proper Map<String, dynamic> format
            List<Map<String, dynamic>> validSongs = [];

            for (var song in items) {
              if (song == null) continue;

              Map<String, dynamic> safeSong = {};
              if (song is Map<String, dynamic>) {
                safeSong = Map<String, dynamic>.from(song);
              } else if (song is Map) {
                song.forEach((key, value) {
                  if (key != null) {
                    safeSong[key.toString()] = value;
                  }
                });
              } else {
                continue;
              }

              // Only add songs with valid videoId
              if (safeSong['videoId'] != null && safeSong['videoId'].toString().trim().isNotEmpty) {
                validSongs.add(safeSong);
              }
            }

            if (validSongs.isNotEmpty) {
              // Find the correct index in the valid songs list
              int adjustedIndex = 0;
              String? currentVideoId = item['videoId']?.toString();
              if (currentVideoId != null) {
                for (int i = 0; i < validSongs.length; i++) {
                  if (validSongs[i]['videoId']?.toString() == currentVideoId) {
                    adjustedIndex = i;
                    break;
                  }
                }
              }

              GetIt.I<MediaPlayer>().playAll(validSongs, index: adjustedIndex);
            }
          } else if (item['endpoint'] != null) {
            Map<String, dynamic> endpoint = {};
            if (item['endpoint'] is Map<String, dynamic>) {
              endpoint = Map<String, dynamic>.from(item['endpoint']);
            } else if (item['endpoint'] is Map) {
              (item['endpoint'] as Map).forEach((key, value) {
                if (key != null) {
                  endpoint[key.toString()] = value;
                }
              });
            }

            Navigator.push(
              context,
              AdaptivePageRoute.create(
                (context) => BrowseScreen(endpoint: endpoint),
              ),
            );
          }
        } catch (e) {
          debugPrint('❌ YTMSongTile: Error: $e');
        }
      },
      title: Text(
        item['title']?.toString() ?? 'Unknown Title',
        maxLines: 2,
      ),
      leading: ClipRRect(
          borderRadius:
              BorderRadius.circular(item['type'] == 'ARTIST' ? 50 : 3),
          child: CachedNetworkImage(
            imageUrl: _getImageUrl(item),
            height: 50,
            width: 50,
            errorWidget: (context, url, error) => Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: const Color(0xFF424242),
                borderRadius: BorderRadius.circular(item['type'] == 'ARTIST' ? 25 : 3),
              ),
              child: const Center(
                child: Text(
                  '♪',
                  style: TextStyle(
                    fontSize: 24,
                    color: Color(0xFFFFFFFF),
                  ),
                ),
              ),
            ),
          )),
      subtitle: Text(
        item['subtitle']?.toString() ?? '',
        maxLines: 1,
      ),
      trailing:
          item['videoId'] == null ? Icon(AdaptiveIcons.chevron_right) : null,
    );
  }

  String _getImageUrl(Map item) {
    try {
      final thumbnails = item['thumbnails'];
      if (thumbnails != null && thumbnails is List && thumbnails.isNotEmpty) {
        final firstThumbnail = thumbnails.first;
        if (firstThumbnail != null && firstThumbnail is Map && firstThumbnail['url'] != null) {
          return firstThumbnail['url'].toString().replaceAll('w540-h225', 'w60-h60');
        }
      }
      return 'https://via.placeholder.com/60x60';
    } catch (e) {
      debugPrint('❌ YTMSongTile: Error getting image URL: $e');
      return 'https://via.placeholder.com/60x60';
    }
  }
}
