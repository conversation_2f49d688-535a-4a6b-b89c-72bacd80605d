import 'dart:async';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get_it/get_it.dart';
import 'package:gyawun/services/yt_audio_stream.dart';
import 'package:just_audio/just_audio.dart';
import 'package:just_audio_background/just_audio_background.dart';

import '../utils/add_history.dart';
import '../ytmusic/ytmusic.dart';
import 'settings_manager.dart';

class MediaPlayer extends ChangeNotifier {
  late AudioPlayer _player;
  final ConcatenatingAudioSource _playlist =
      ConcatenatingAudioSource(children: []);

  final _loudnessEnhancer = AndroidLoudnessEnhancer();
  AndroidEqualizer? _equalizer;
  AndroidEqualizerParameters? _equalizerParams;

  List<IndexedAudioSource>? _songList = [];
  // MediaItem? _currentSong;
  final ValueNotifier<MediaItem?> _currentSongNotifier = ValueNotifier(null);
  final ValueNotifier<int?> _currentIndex = ValueNotifier(null);
  final ValueNotifier<ButtonState> _buttonState =
      ValueNotifier(ButtonState.loading);
  Timer? _timer;
  final ValueNotifier<Duration?> _timerDuration = ValueNotifier(null);

  final ValueNotifier<LoopMode> _loopMode = ValueNotifier(LoopMode.off);

  final ValueNotifier<ProgressBarState> _progressBarState =
      ValueNotifier(ProgressBarState());

  bool _shuffleModeEnabled = false;

  MediaPlayer() {
    if (Platform.isAndroid) {
      _equalizer = AndroidEqualizer();
    }
    final AudioPipeline pipeline = AudioPipeline(
      androidAudioEffects: [
        if (Platform.isAndroid) _equalizer!,
        _loudnessEnhancer,
      ],
    );
    _player = AudioPlayer(audioPipeline: pipeline);
    GetIt.I.registerSingleton<AndroidLoudnessEnhancer>(_loudnessEnhancer);
    if (Platform.isAndroid) {
      GetIt.I.registerSingleton<AndroidEqualizer>(_equalizer!);
    }
    _init();
  }
  AudioPlayer get player => _player;
  ConcatenatingAudioSource get playlist => _playlist;
  List<IndexedAudioSource>? get songList => _songList;
  ValueNotifier<MediaItem?> get currentSongNotifier => _currentSongNotifier;
  ValueNotifier<int?> get currentIndex => _currentIndex;
  ValueNotifier<ButtonState> get buttonState => _buttonState;

  ValueNotifier<ProgressBarState> get progressBarState => _progressBarState;
  bool get shuffleModeEnabled => _shuffleModeEnabled;
  ValueNotifier<LoopMode> get loopMode => _loopMode;
  ValueNotifier<Duration?> get timerDuration => _timerDuration;
  _init() async {
    await _loadLoudnessEnhancer();
    await _loadEqualizer();
    await _player.setAudioSource(_playlist);
    _listenToChangesInPlaylist();
    _listenToPlaybackState();
    _listenToCurrentPosition();
    _listenToBufferedPosition();
    _listenToTotalDuration();
    _listenToChangesInSong();
    _listenToShuffle();
    Timer.periodic(const Duration(seconds: 10), (timer) {
      if (currentSongNotifier.value != null && _player.playing) {
        GetIt.I<YTMusic>()
            .addPlayingStats(currentSongNotifier.value!.id, _player.position);
      }
    });
  }

  _loadLoudnessEnhancer() async {
    await _loudnessEnhancer
        .setEnabled(GetIt.I<SettingsManager>().loudnessEnabled);

    await _loudnessEnhancer
        .setTargetGain(GetIt.I<SettingsManager>().loudnessTargetGain);
  }

  _loadEqualizer() async {
    if (!Platform.isAndroid) return;
    await _equalizer!.setEnabled(GetIt.I<SettingsManager>().equalizerEnabled);
    _equalizer!.parameters.then((value) async {
      _equalizerParams ??= value;
      final List<AndroidEqualizerBand> bands = _equalizerParams!.bands;
      if (GetIt.I<SettingsManager>().equalizerBandsGain.isEmpty) {
        GetIt.I<SettingsManager>().equalizerBandsGain =
            List.generate(bands.length, (index) => 0.0);
      }

      List<double> equalizerBandsGain =
          GetIt.I<SettingsManager>().equalizerBandsGain;
      for (var e in bands) {
        final gain =
            equalizerBandsGain.isNotEmpty ? equalizerBandsGain[e.index] : 0.0;
        _equalizerParams!.bands[e.index].setGain(gain);
      }
    });
  }

  Future<void> setLoudnessEnabled(bool value) async {
    await _loudnessEnhancer.setEnabled(value);
    GetIt.I<SettingsManager>().loudnessEnabled = value;
  }

  Future<void> setEqualizerEnabled(bool value) async {
    await _equalizer?.setEnabled(value);
    GetIt.I<SettingsManager>().equalizerEnabled = value;
  }

  Future<void> setLoudnessTargetGain(double value) async {
    await _loudnessEnhancer.setTargetGain(value);
    GetIt.I<SettingsManager>().loudnessTargetGain = value;
  }

  void _listenToChangesInPlaylist() async {
    _player.sequenceStream.listen((playlist) {
      if (playlist == _songList) return;
      bool shouldAdd = false;
      if ((_songList == null || _songList!.isEmpty) &&
          playlist != null &&
          playlist.isNotEmpty) {
        shouldAdd = true;
      }
      if (playlist == null || playlist.isEmpty) {
        _currentSongNotifier.value = null;
        _currentIndex.value == null;
        _songList = [];
      } else {
        _currentIndex.value ??= 0;
        _songList = playlist;

        _currentSongNotifier.value ??=
            (_songList!.length > _currentIndex.value!)
                ? _songList![_currentIndex.value!].tag
                : null;
      }
      if (shouldAdd == true && _currentSongNotifier.value != null) {
        addHistory(_currentSongNotifier.value!.extras!);
      }
      notifyListeners();
    });
  }

  void _listenToPlaybackState() {
    _player.playerStateStream.listen((event) {
      final isPlaying = event.playing;
      final processingState = event.processingState;
      if (processingState == ProcessingState.loading ||
          processingState == ProcessingState.buffering) {
        _buttonState.value = ButtonState.loading;
      } else if (!isPlaying || processingState == ProcessingState.idle) {
        _buttonState.value = ButtonState.paused;
      } else if (processingState != ProcessingState.completed) {
        _buttonState.value = ButtonState.playing;
      } else {
        _player.seek(Duration.zero);
        _player.pause();
      }
    });
  }

  void _listenToCurrentPosition() {
    _player.positionStream.listen((position) {
      final oldState = _progressBarState.value;
      if (oldState.current != position) {
        _progressBarState.value = ProgressBarState(
          current: position,
          buffered: oldState.buffered,
          total: oldState.total,
        );
      }
    });
  }

  void _listenToBufferedPosition() async {
    _player.bufferedPositionStream.listen((position) {
      final oldState = _progressBarState.value;
      if (oldState.buffered != position) {
        _progressBarState.value = ProgressBarState(
          current: oldState.current,
          buffered: position,
          total: oldState.total,
        );
      }
    });
  }

  void _listenToTotalDuration() async {
    _player.durationStream.listen((position) {
      final oldState = _progressBarState.value;
      if (oldState.total != position) {
        _progressBarState.value = ProgressBarState(
          current: oldState.current,
          buffered: oldState.buffered,
          total: position ?? Duration.zero,
        );
      }
    });
  }

  void _listenToShuffle() {
    _player.shuffleModeEnabledStream.listen((data) {
      _shuffleModeEnabled = data;
      notifyListeners();
    });
  }

  void _listenToChangesInSong() async {
    _player.currentIndexStream.listen((index) {
      if (_songList != null && _currentIndex.value != index) {
        _currentIndex.value = index;
        _currentSongNotifier.value = index != null &&
                songList!.isNotEmpty &&
                _songList?.elementAt(index) != null
            ? _songList![index].tag
            : null;
        if (_songList!.isNotEmpty && _currentIndex.value != null) {
          MediaItem item = _songList![_currentIndex.value!].tag;
          addHistory(item.extras!);
        }
      }
    });
  }

  changeLoopMode() {
    switch (_loopMode.value) {
      case LoopMode.off:
        _loopMode.value = LoopMode.all;

        break;
      case LoopMode.all:
        _loopMode.value = LoopMode.one;
        break;
      default:
        _loopMode.value = LoopMode.off;
        break;
    }
    _player.setLoopMode(_loopMode.value);
  }

  Future<void> skipSilence(bool value) async {
    await _player.setSkipSilenceEnabled(value);
    GetIt.I<SettingsManager>().skipSilence = value;
  }

  Future<AudioSource> _getAudioSource(Map<String, dynamic> song) async {
    // Ensure song is not null and has required fields
    if (song['videoId'] == null || song['videoId'].toString().isEmpty) {
      throw ArgumentError('Song must be valid and have a videoId');
    }

    String? artUrl;
    if (song['thumbnails'] != null && song['thumbnails'].isNotEmpty) {
      artUrl = song['thumbnails'].first['url']?.replaceAll('w60-h60', 'w225-h225');
    }

    String? artistNames;
    if (song['artists'] != null && song['artists'] is List) {
      artistNames = (song['artists'] as List)
          .where((artist) => artist != null && artist['name'] != null)
          .map((artist) => artist['name'].toString())
          .join(', ');
    }

    MediaItem tag = MediaItem(
      id: song['videoId'],
      title: song['title']?.toString() ?? 'Unknown Title',
      album: song['album']?['name']?.toString(),
      artUri: artUrl != null ? Uri.tryParse(artUrl) : null,
      artist: artistNames,
      extras: song,
    );
    AudioSource audioSource;
    bool isDownloaded = song['status'] == 'DOWNLOADED' &&
        song['path'] != null &&
        (await File(song['path']).exists());
    if (isDownloaded) {
      audioSource = AudioSource.file(song['path'], tag: tag);
    } else {
      // audioSource = AudioSource.uri(
      //   Uri.parse(
      //       'https://invidious.nerdvpn.de/latest_version?id=${song['videoId']}'),
      //   tag: tag,
      // );

      audioSource = YouTubeAudioSource(
        videoId: song['videoId'],
        quality: GetIt.I<SettingsManager>().streamingQuality.name.toLowerCase(),
        tag: tag,
      );
    }
    return audioSource;
  }

  // void download() async {
  //   List<String> qualVidOptions = ['480p', '720p', '1080p', '2K', '4K'];
  //   List<String> qualAudOptions = ['64k', '128k', '192k', '256k', '320k'];

  //   String command =
  //       'yt-dlp -f \'bestvideo[height<=${qualVid.replaceAll('p', '')}]'
  //       '+bestaudio/best[height<=${qualVid.replaceAll('p', '')}]\' $ytUrl';
  //   if (dlAud) command += ' -x --audio-format mp3 --audio-quality ${qualAud}';
  //   if (dlThumb) command += ' --write-thumbnail';
  //   if (timeStart != null && timeEnd != null) {
  //     command +=
  //         ' --postprocessor-args "-ss ${timeStart!.format(context)} -to ${timeEnd!.format(context)}"';
  //   }
  //   Shell shell = Shell();
  //   if (kDebugMode) {
  //     print("Running command: ==================================== ");
  //     print(command);
  //   }
  //   await shell.run(command);
  // }

  Future<void> playSong(Map<String, dynamic>? song,
      {bool autoFetch = true}) async {
    try {
      // Validate input
      if (song == null) {
        debugPrint('❌ playSong: Song is null');
        return;
      }

      // Ensure song is properly typed
      Map<String, dynamic> validSong = Map<String, dynamic>.from(song);

      // Validate required fields
      String? videoId = validSong['videoId']?.toString();
      if (videoId == null || videoId.trim().isEmpty) {
        debugPrint('❌ playSong: Song missing videoId');
        return;
      }

      // Ensure required fields exist
      if (validSong['title'] == null || validSong['title'].toString().trim().isEmpty) {
        validSong['title'] = 'Unknown Song';
      }

      // Ensure thumbnails exist
      if (validSong['thumbnails'] == null ||
          (validSong['thumbnails'] is List && (validSong['thumbnails'] as List).isEmpty)) {
        validSong['thumbnails'] = [
          {'url': 'https://img.youtube.com/vi/$videoId/mqdefault.jpg'}
        ];
      }

      // Ensure artists exist
      if (validSong['artists'] == null) {
        validSong['artists'] = [{'name': 'Unknown Artist'}];
      }

      await _player.pause();
      await _playlist.clear();
      final source = await _getAudioSource(validSong);
      await _playlist.add(source);
      await _player.load();
      _player.play();

      if (autoFetch == true && validSong['status'] != 'DOWNLOADED') {
        List nextSongs =
            await GetIt.I<YTMusic>().getNextSongList(videoId: videoId);
        if (nextSongs.isNotEmpty) {
          nextSongs.removeAt(0);
          await _addSongListToQueue(nextSongs);
        }
      }

      debugPrint('✅ playSong: Successfully started playing ${validSong['title']}');

    } catch (e, stackTrace) {
      debugPrint('❌ playSong: Error playing song: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }

  Future<void> playNext(Map<String, dynamic>? song) async {
    try {
      if (song == null) {
        debugPrint('❌ playNext: Song is null');
        return;
      }

      Map<String, dynamic> validSong = Map<String, dynamic>.from(song);

      if (validSong['videoId'] != null) {
        AudioSource audioSource = await _getAudioSource(validSong);

        if (_playlist.length > 0) {
          await _playlist.insert((_player.currentIndex ?? -1) + 1, audioSource);
        } else {
          await _playlist.add(audioSource);
        }
      } else if (validSong['playlistId'] != null) {
        List songs =
            await GetIt.I<YTMusic>().getPlaylistSongs(validSong['playlistId']);
        await _addSongListToQueue(songs, isNext: true);
      }
      if (!_player.playing) {
        await _player.load();
        _player.play();
      }
    } catch (e) {
      debugPrint('❌ playNext: Error: $e');
    }
  }

  // COMPLETELY NEW BULLETPROOF PLAY ALL FUNCTION
  Future<void> playAllSongs(dynamic songsList, {int startIndex = 0}) async {
    try {
      debugPrint('🎵 PlayAllSongs: Starting with ${songsList?.runtimeType}');

      // Step 1: Validate input
      if (songsList == null) {
        debugPrint('❌ PlayAllSongs: Input is null');
        return;
      }

      List<dynamic> rawSongs = [];
      if (songsList is List) {
        rawSongs = songsList;
      } else {
        debugPrint('❌ PlayAllSongs: Input is not a List: ${songsList.runtimeType}');
        return;
      }

      if (rawSongs.isEmpty) {
        debugPrint('❌ PlayAllSongs: Song list is empty');
        return;
      }

      debugPrint('🎵 PlayAllSongs: Processing ${rawSongs.length} songs');

      // Step 2: Filter and validate songs
      List<Map<String, dynamic>> validatedSongs = [];

      for (int i = 0; i < rawSongs.length; i++) {
        try {
          var rawSong = rawSongs[i];

          if (rawSong == null) {
            debugPrint('⚠️ PlayAllSongs: Song $i is null, skipping');
            continue;
          }

          // Convert to Map<String, dynamic>
          Map<String, dynamic> song;
          if (rawSong is Map<String, dynamic>) {
            song = Map<String, dynamic>.from(rawSong);
          } else if (rawSong is Map) {
            song = <String, dynamic>{};
            rawSong.forEach((key, value) {
              if (key != null) {
                song[key.toString()] = value;
              }
            });
          } else {
            debugPrint('⚠️ PlayAllSongs: Song $i is not a Map: ${rawSong.runtimeType}, skipping');
            continue;
          }

          // Validate required fields
          String? videoId = song['videoId']?.toString();
          String? title = song['title']?.toString();

          if (videoId == null || videoId.trim().isEmpty) {
            debugPrint('⚠️ PlayAllSongs: Song $i missing videoId, skipping');
            continue;
          }

          if (title == null || title.trim().isEmpty) {
            song['title'] = 'Unknown Song';
          }

          // Ensure thumbnails exist
          if (song['thumbnails'] == null ||
              (song['thumbnails'] is List && (song['thumbnails'] as List).isEmpty)) {
            song['thumbnails'] = [
              {'url': 'https://img.youtube.com/vi/$videoId/mqdefault.jpg'}
            ];
          }

          // Ensure artists exist
          if (song['artists'] == null) {
            song['artists'] = [{'name': 'Unknown Artist'}];
          }

          validatedSongs.add(song);
          debugPrint('✅ PlayAllSongs: Added song: ${song['title']}');

        } catch (e) {
          debugPrint('❌ PlayAllSongs: Error processing song $i: $e');
          continue;
        }
      }

      if (validatedSongs.isEmpty) {
        debugPrint('❌ PlayAllSongs: No valid songs after filtering');
        return;
      }

      debugPrint('🎵 PlayAllSongs: ${validatedSongs.length} valid songs ready');

      // Step 3: Clear current playlist
      await _playlist.clear();
      debugPrint('🎵 PlayAllSongs: Playlist cleared');

      // Step 4: Add songs to playlist
      for (int i = 0; i < validatedSongs.length; i++) {
        try {
          var song = validatedSongs[i];
          var audioSource = await _getAudioSource(song);
          await _playlist.add(audioSource);
          debugPrint('✅ PlayAllSongs: Added to playlist: ${song['title']}');
        } catch (e) {
          debugPrint('❌ PlayAllSongs: Failed to add song $i: $e');
          continue;
        }
      }

      if (_playlist.length == 0) {
        debugPrint('❌ PlayAllSongs: No songs added to playlist');
        return;
      }

      // Step 5: Start playback
      int safeIndex = startIndex.clamp(0, _playlist.length - 1);
      debugPrint('🎵 PlayAllSongs: Starting playback at index $safeIndex');

      await _player.seek(Duration.zero, index: safeIndex);

      if (!_player.playing) {
        await _player.play();
      }

      debugPrint('🎉 PlayAllSongs: Successfully started playback!');

    } catch (e, stackTrace) {
      debugPrint('💥 PlayAllSongs: Critical error: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }

  // Legacy method for backward compatibility
  playAll(dynamic songs, {int index = 0}) async {
    return playAllSongs(songs, startIndex: index);
  }

  Future<void> addToQueue(Map<String, dynamic>? song) async {
    try {
      if (song == null) {
        debugPrint('❌ addToQueue: Song is null');
        return;
      }

      Map<String, dynamic> validSong = Map<String, dynamic>.from(song);

      if (validSong['videoId'] != null) {
        await _playlist.add(await _getAudioSource(validSong));
      } else if (validSong['playlistId'] != null) {
        List songs =
            await GetIt.I<YTMusic>().getPlaylistSongs(validSong['playlistId']);
        await _addSongListToQueue(songs, isNext: false);
      }
    } catch (e) {
      debugPrint('❌ addToQueue: Error: $e');
    }
  }

  Future<void> startRelated(Map<String, dynamic> song,
      {bool radio = false, bool shuffle = false, bool isArtist = false}) async {
    await _playlist.clear();
    if (!isArtist) {
      await addToQueue(song);
    }
    List songs = await GetIt.I<YTMusic>().getNextSongList(
        videoId: song['videoId'],
        playlistId: song['playlistId'],
        radio: radio,
        shuffle: shuffle);
    songs.removeAt(0);
    await _addSongListToQueue(songs, isNext: false);
    await _player.load();
    _player.play();
  }

  Future<void> startPlaylistSongs(Map? endpoint) async {
    try {
      if (endpoint == null) {
        debugPrint('❌ startPlaylistSongs: Endpoint is null');
        return;
      }

      Map<String, dynamic> safeEndpoint = Map<String, dynamic>.from(endpoint);

      await playlist.clear();
      List songs = await GetIt.I<YTMusic>().getNextSongList(
          playlistId: safeEndpoint['playlistId'], params: safeEndpoint['params']);
      await _addSongListToQueue(songs);
      await _player.load();
      _player.play();
    } catch (e) {
      debugPrint('❌ startPlaylistSongs: Error: $e');
    }
  }

  Future<void> stop() async {
    await _player.stop();
    await _playlist.clear();
    await _player.seek(Duration.zero, index: 0);
    _currentIndex.value = null;
    _currentSongNotifier.value = null;
    notifyListeners();
  }

  // Helper method to safely convert List<dynamic> to List<Map<String, dynamic>>
  List<Map<String, dynamic>> _validateSongList(List<dynamic> songs) {
    List<Map<String, dynamic>> validSongs = [];

    for (var song in songs) {
      if (song == null) continue;

      try {
        Map<String, dynamic> songMap;
        if (song is Map<String, dynamic>) {
          songMap = song;
        } else if (song is Map) {
          songMap = Map<String, dynamic>.from(song);
        } else {
          continue;
        }

        // Validate required fields
        if (songMap['videoId'] != null && songMap['title'] != null) {
          validSongs.add(songMap);
        }
      } catch (e) {
        debugPrint('Error validating song: $e');
        continue;
      }
    }

    return validSongs;
  }

  _addSongListToQueue(List songs, {bool isNext = false}) async {
    try {
      // Validate and convert songs
      List<Map<String, dynamic>> validSongs = _validateSongList(songs);

      if (validSongs.isEmpty) {
        debugPrint('_addSongListToQueue: No valid songs to add');
        return;
      }

      int index = _playlist.length;
      if (isNext) {
        index = _player.sequence.isEmpty
            ? 0
            : (currentIndex.value ?? 0) + 1;
      }

      for (var song in validSongs) {
        try {
          AudioSource audioSource = await _getAudioSource(song);
          await _playlist.insert(index, audioSource);
          index++;
          debugPrint('_addSongListToQueue: Added ${song['title']}');

        } catch (e) {
          debugPrint('_addSongListToQueue: Error adding song ${song['title']}: $e');
          continue;
        }
      }
    } catch (e) {
      debugPrint('_addSongListToQueue: Critical error: $e');
    }
  }

  setTimer(Duration duration) {
    int seconds = duration.inSeconds;
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      seconds--;
      _timerDuration.value = Duration(seconds: seconds);
      if (seconds == 0) {
        cancelTimer();
        _player.pause();
      }
      notifyListeners();
    });
  }

  cancelTimer() {
    _timerDuration.value = null;
    _timer?.cancel();
    notifyListeners();
  }
}

enum ButtonState { loading, paused, playing }

enum LoopState { off, all, one }

class ProgressBarState {
  Duration current;
  Duration buffered;
  Duration total;
  ProgressBarState(
      {this.current = Duration.zero,
      this.buffered = Duration.zero,
      this.total = Duration.zero});
}
