import 'package:flutter/material.dart';

Color greyColor = Colors.grey.withAlpha(100);
Color darkGreyColor = Colors.grey.withAlpha(70);
const MaterialColor primaryBlack = MaterialColor(
  0xFF000000,
  <int, Color>{
    50: Color.fromRGBO(0, 0, 0, .1),
    100: Color.fromRGBO(0, 0, 0, .2),
    200: Color.fromRGBO(0, 0, 0, .3),
    300: Color.fromRGBO(0, 0, 0, .4),
    400: Color.fromRGBO(0, 0, 0, .5),
    500: Color.fromRGBO(0, 0, 0, .6),
    600: Color.fromRGBO(0, 0, 0, .7),
    700: Color.fromRGBO(0, 0, 0, .8),
    800: Color.fromRGBO(0, 0, 0, .9),
    900: Color.fromRGBO(0, 0, 0, 1),
  },
);

const MaterialColor primaryWhite = MaterialColor(
  0xFFFFFFFF,
  <int, Color>{
    50: Color.fromRGBO(255, 255, 255, .1),
    100: Color.fromRGBO(255, 255, 255, .2),
    200: Color.fromRGBO(255, 255, 255, .3),
    300: Color.fromRGBO(255, 255, 255, .4),
    400: Color.fromRGBO(255, 255, 255, .5),
    500: Color.fromRGBO(255, 255, 255, .6),
    600: Color.fromRGBO(255, 255, 255, .7),
    700: Color.fromRGBO(255, 255, 255, .8),
    800: Color.fromRGBO(255, 255, 255, .9),
    900: Color.fromRGBO(255, 255, 255, 1),
  },
);

// iOS-style constants
const double iOSBorderRadius = 16.0;
const double iOSCardRadius = 12.0;
const double iOSButtonRadius = 10.0;
const double iOSSmallRadius = 8.0;

// iOS-style colors
const Color iOSBlue = Color(0xFF007AFF);
const Color iOSGreen = Color(0xFF34C759);
const Color iOSRed = Color(0xFFFF3B30);
const Color iOSOrange = Color(0xFFFF9500);
const Color iOSPurple = Color(0xFFAF52DE);
const Color iOSGray = Color(0xFF8E8E93);
const Color iOSLightGray = Color(0xFFF2F2F7);
const Color iOSDarkGray = Color(0xFF1C1C1E);
