import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'colors.dart';

// ColorScheme.fromSeed(
//             seedColor: accentColor,
//             brightness: darkScheme.brightness,
//             primary: accentColor,
//             primaryContainer: accentColor,
//             onPrimaryContainer: Colors.black,
//             surface: accentColor.withAlpha(10),
//           )
final defaultFontStyle = GoogleFonts.poppins();
ColorScheme darkScheme = const ColorScheme.dark();
ThemeData darkTheme({required ColorScheme colorScheme}) {
  return ThemeData.dark().copyWith(
    colorScheme: colorScheme.copyWith(
      surface: const Color(0xFF000000), // Pure black for OLED displays
      surfaceContainerLow: const Color(0xFF111111), // Very dark for cards
      surfaceContainer: const Color(0xFF1A1A1A), // Medium surface
      surfaceContainerHigh: const Color(0xFF2A2A2A), // Higher surface
      onSurface: Colors.white, // Ensure text is white on dark surfaces
      onSurfaceVariant: Colors.white.withValues(alpha: 0.8),
    ),
    scaffoldBackgroundColor: Platform.isWindows
        ? Colors.transparent
        : const Color(0xFF000000), // Pure black background
    primaryColor: colorScheme.primary,
    cardColor: const Color(0xFF111111), // Very dark cards
    appBarTheme: AppBarTheme(
      centerTitle: true,
      backgroundColor: Colors.transparent,
      surfaceTintColor: Platform.isWindows ? Colors.transparent : null,
      elevation: 0,
      systemOverlayStyle: const SystemUiOverlayStyle(
        statusBarBrightness: Brightness.dark,
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.transparent,
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: iOSBlue,
        foregroundColor: Colors.white,
        elevation: 8,
        shadowColor: iOSBlue.withValues(alpha: 0.3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(iOSBorderRadius),
        ),
      ),
    ),
    cardTheme: CardThemeData(
      color: const Color(0xFF111111),
      elevation: 12,
      shadowColor: Colors.black.withValues(alpha: 0.5),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(iOSCardRadius),
      ),
    ),
    textTheme: TextTheme(
      headlineLarge: defaultFontStyle.copyWith(color: Colors.white),
      headlineMedium: defaultFontStyle.copyWith(color: Colors.white),
      headlineSmall: defaultFontStyle.copyWith(color: Colors.white),
      bodyLarge: defaultFontStyle.copyWith(color: Colors.white),
      bodyMedium: defaultFontStyle.copyWith(color: Colors.white),
      bodySmall: defaultFontStyle.copyWith(color: Colors.white),
      displayLarge: defaultFontStyle.copyWith(color: Colors.white),
      displayMedium: defaultFontStyle.copyWith(color: Colors.white),
      displaySmall: defaultFontStyle.copyWith(color: Colors.white),
      titleLarge: defaultFontStyle.copyWith(color: Colors.white),
      titleMedium: defaultFontStyle.copyWith(color: Colors.white),
      titleSmall: defaultFontStyle.copyWith(color: Colors.white),
      labelLarge: defaultFontStyle.copyWith(color: Colors.white),
      labelMedium: defaultFontStyle.copyWith(color: Colors.white),
      labelSmall: defaultFontStyle.copyWith(color: Colors.white),
    ),
  );
}
