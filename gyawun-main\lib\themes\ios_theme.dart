import 'package:flutter/material.dart';
import 'colors.dart';

class IOSTheme {
  // iOS-style card decoration
  static BoxDecoration cardDecoration({
    Color? color,
    bool isDark = false,
  }) {
    return BoxDecoration(
      color: color ?? (isDark ? iOSDarkGray : Colors.white),
      borderRadius: BorderRadius.circular(iOSCardRadius),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
          blurRadius: 10,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  // iOS-style button decoration
  static BoxDecoration buttonDecoration({
    Color? color,
    bool isDark = false,
  }) {
    return BoxDecoration(
      color: color ?? iOSBlue,
      borderRadius: BorderRadius.circular(iOSButtonRadius),
      boxShadow: [
        BoxShadow(
          color: (color ?? iOSBlue).withValues(alpha: 0.3),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  // iOS-style container decoration
  static BoxDecoration containerDecoration({
    Color? color,
    bool isDark = false,
  }) {
    return BoxDecoration(
      color: color ?? (isDark ? iOSDarkGray : iOSLightGray),
      borderRadius: BorderRadius.circular(iOSBorderRadius),
    );
  }

  // iOS-style list tile decoration
  static BoxDecoration listTileDecoration({
    bool isDark = false,
  }) {
    return BoxDecoration(
      color: isDark ? iOSDarkGray : Colors.white,
      borderRadius: BorderRadius.circular(iOSCardRadius),
      border: Border.all(
        color: isDark ? Colors.white.withValues(alpha: 0.1) : Colors.black.withValues(alpha: 0.1),
        width: 0.5,
      ),
    );
  }

  // iOS-style text styles
  static TextStyle titleStyle({
    bool isDark = false,
    double fontSize = 20,
    FontWeight fontWeight = FontWeight.w600,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: isDark ? Colors.white : Colors.black,
      letterSpacing: -0.5,
    );
  }

  static TextStyle bodyStyle({
    bool isDark = false,
    double fontSize = 16,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: FontWeight.w400,
      color: isDark ? Colors.white.withValues(alpha: 0.9) : Colors.black.withValues(alpha: 0.8),
    );
  }

  static TextStyle captionStyle({
    bool isDark = false,
    double fontSize = 14,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: FontWeight.w400,
      color: isDark ? Colors.white.withValues(alpha: 0.6) : iOSGray,
    );
  }
}
