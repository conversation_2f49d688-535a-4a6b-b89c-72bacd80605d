import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';

class PerformanceConfig {
  static void initialize() {
    // Enable performance optimizations
    _enablePerformanceOptimizations();
    
    // Configure scroll physics
    _configureScrollPhysics();
    
    // Optimize rendering
    _optimizeRendering();
  }

  static void _enablePerformanceOptimizations() {
    // Enable hardware acceleration
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    
    // Optimize memory usage
    PaintingBinding.instance.imageCache.maximumSize = 100;
    PaintingBinding.instance.imageCache.maximumSizeBytes = 50 * 1024 * 1024; // 50MB
  }

  static void _configureScrollPhysics() {
    // Set default scroll physics for better performance
    ScrollConfiguration.of;
  }

  static void _optimizeRendering() {
    // Enable repaint rainbow for debugging (disable in production)
    // debugRepaintRainbowEnabled = false;
    
    // Optimize layer tree
    debugProfileBuildsEnabled = false;
    debugProfilePaintsEnabled = false;
  }

  // Optimized scroll physics for smooth scrolling
  static const ScrollPhysics optimizedScrollPhysics = BouncingScrollPhysics(
    parent: AlwaysScrollableScrollPhysics(),
  );

  // Enhanced scroll physics for even smoother performance
  static const ScrollPhysics enhancedScrollPhysics = BouncingScrollPhysics(
    parent: ClampingScrollPhysics(),
  );

  // Fast scroll physics for quick navigation
  static const ScrollPhysics fastScrollPhysics = BouncingScrollPhysics(
    parent: AlwaysScrollableScrollPhysics(),
  );

  // Optimized list view configuration
  static const double defaultCacheExtent = 800.0;
  static const double horizontalCacheExtent = 1200.0;
  static const double fastCacheExtent = 1500.0;
  static const int defaultSemanticChildCount = 50;

  // Image loading optimizations
  static const Duration fastFadeInDuration = Duration(milliseconds: 100);
  static const Duration fastFadeOutDuration = Duration(milliseconds: 50);

  // Animation durations for better performance
  static const Duration fastAnimationDuration = Duration(milliseconds: 150);
  static const Duration normalAnimationDuration = Duration(milliseconds: 250);
  static const Duration slowAnimationDuration = Duration(milliseconds: 350);

  // Curves for smooth animations
  static const Curve fastCurve = Curves.easeOut;
  static const Curve normalCurve = Curves.easeInOut;
  static const Curve bouncyCurve = Curves.elasticOut;
}

// Custom scroll behavior for better performance
class OptimizedScrollBehavior extends ScrollBehavior {
  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    return const BouncingScrollPhysics(
      parent: AlwaysScrollableScrollPhysics(),
    );
  }

  @override
  Widget buildScrollbar(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    return child; // Remove scrollbars for better performance on mobile
  }

  @override
  Widget buildOverscrollIndicator(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    return child; // Use default overscroll indicator
  }
}

// Optimized list view builder
class OptimizedListView extends StatelessWidget {
  final int itemCount;
  final Widget Function(BuildContext, int) itemBuilder;
  final ScrollController? controller;
  final Axis scrollDirection;
  final EdgeInsetsGeometry? padding;
  final double? itemExtent;

  const OptimizedListView({
    Key? key,
    required this.itemCount,
    required this.itemBuilder,
    this.controller,
    this.scrollDirection = Axis.vertical,
    this.padding,
    this.itemExtent,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: controller,
      scrollDirection: scrollDirection,
      padding: padding,
      itemExtent: itemExtent,
      physics: PerformanceConfig.optimizedScrollPhysics,
      cacheExtent: PerformanceConfig.defaultCacheExtent,
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      addAutomaticKeepAlives: false, // Improve memory usage
      addRepaintBoundaries: true, // Optimize repaints
      addSemanticIndexes: false, // Disable for better performance
    );
  }
}

// Optimized grid view
class OptimizedGridView extends StatelessWidget {
  final int itemCount;
  final Widget Function(BuildContext, int) itemBuilder;
  final SliverGridDelegate gridDelegate;
  final ScrollController? controller;
  final EdgeInsetsGeometry? padding;

  const OptimizedGridView({
    Key? key,
    required this.itemCount,
    required this.itemBuilder,
    required this.gridDelegate,
    this.controller,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      controller: controller,
      padding: padding,
      gridDelegate: gridDelegate,
      physics: PerformanceConfig.optimizedScrollPhysics,
      cacheExtent: PerformanceConfig.defaultCacheExtent,
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: true,
      addSemanticIndexes: false,
    );
  }
}
