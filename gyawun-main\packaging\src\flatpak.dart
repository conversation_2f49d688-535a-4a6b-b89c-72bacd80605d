import 'dart:io';

import 'package:logging/logging.dart';
import 'package:yaml/yaml.dart';

import 'utils.dart';

updateFlatpak(YamlMap packagingYaml,Logger log){
  final flatpakFile = File('packaging/flatpak/com.jhelum.gyawun.yaml');
  if (!flatpakFile.existsSync()) {
    log.severe('❌ packaging/flatpak/com.jhelum.gyawun.yaml not found.');
    exit(1);
  }
  final flatpakContent = flatpakFile.readAsStringSync();
  log.info('packaging/flatpak/com.jhelum.gyawun.yaml read successfully.');
  
  copyFile(log,
    srcPath: 'packaging/src/gyawun.desktop',
    destDirPath: 'packaging/flatpak',
    name: 'gyawun.desktop'
  );
  copyFile(log,
    srcPath: 'packaging/src/icon-512x512.png',
    destDirPath: 'packaging/flatpak',
    name: 'com.jhelum.gyawun.png'
  );
}
