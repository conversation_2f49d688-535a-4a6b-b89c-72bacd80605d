import 'dart:io';

import 'package:logging/logging.dart';
import 'package:yaml/yaml.dart';

import 'utils.dart';

updateSnapcraft(YamlMap packagingYaml,Logger log){
  final snapcraftFile = File('snap/snapcraft.yaml');
  if (!snapcraftFile.existsSync()) {
    log.severe('❌ snap/snapcraft.yaml not found.');
    exit(1);
  }
  final snapcraftContent = snapcraftFile.readAsStringSync();
  log.info('snapcraft.yaml read successfully.');

  copyFile(log,
    srcPath: 'packaging/src/gyawun.desktop',
    destDirPath: 'snap/gui',
    name: 'gyawun.desktop'
  );
  copyFile(log,
    srcPath: 'packaging/src/icon-512x512.png',
    destDirPath: 'snap/gui',
    name: 'com.jhelum.gyawun.png'
  );
}