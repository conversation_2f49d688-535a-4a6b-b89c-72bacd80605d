name: gyawun
description: "A new Flutter project."
publish_to: 'none'
version: 2.0.13+38

environment:
  sdk: '>=3.4.1 <4.0.0'
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  cupertino_icons: ^1.0.6
  go_router: ^15.1.2
  google_fonts: ^6.2.1
  intl: ^0.20.2
  http: ^1.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  youtube_explode_dart:
    git:
      url: https://github.com/sheikhhaziq/youtube_explode_dart.git
  expandable_page_view: ^1.0.17
  get_it: ^8.0.3
  cached_network_image: ^3.3.1
  flutter_typeahead: ^5.2.0
  expandable_text: ^2.3.0
  salomon_bottom_bar: ^3.3.2
  share_plus: ^11.0.0
  provider: ^6.1.2
  flutter_staggered_grid_view: ^0.7.0
  flutter_swipe_action_cell: ^3.1.3
  just_audio: ^0.10.3
  just_audio_background: ^0.0.1-beta.14
  audio_video_progress_bar: ^2.0.3
  sliding_up_panel: ^2.0.0+1
  url_launcher: ^6.3.0
  country_picker: ^2.0.26
  palette_generator: ^0.3.3+4  # Discontinued
  text_scroll: ^0.2.0
  duration_picker: ^1.2.0
  path_provider: ^2.1.3
  
  path: ^1.9.0
  file_picker: ^10.1.9
  dynamic_color: ^1.7.0
  audiotags: ^1.4.3
  pub_semver: ^2.1.4
  device_info_plus: ^11.4.0
  flutter_markdown: ^0.7.3 # Discontinued
  flutter_expandable_fab: ^2.1.0
  flutter_lyric: ^2.0.4+6
  media_kit_libs_windows_audio: any
  media_kit_libs_linux: any
  just_audio_media_kit: ^2.0.6
  media_kit_native_event_loop: any
  flutter_acrylic: ^1.1.4
  fluent_ui: ^4.9.2
  window_manager: ^0.4.3
  receive_sharing_intent:
    git:
      url: https://github.com/sheikhhaziq/receive_sharing_intent.git
  fl_toast: ^3.2.0 # Discontinued
  flutter_colorpicker: ^1.1.0
  easy_folder_picker: ^1.3.3
  permission_handler: ^11.4.0
  image_picker: ^1.0.7
  shared_preferences: ^2.2.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  change_app_package_name: ^1.3.0
  flutter_native_splash: ^2.4.1
flutter:
  uses-material-design: true
  generate: true
  assets:
    - assets/images/
flutter_intl:
  enabled: true
